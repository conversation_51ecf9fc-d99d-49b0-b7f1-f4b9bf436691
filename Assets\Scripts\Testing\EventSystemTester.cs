using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UIElements;
using BlastingDesign.Core.Input;
using BlastingDesign.Utils;

namespace BlastingDesign.Testing
{
    /// <summary>
    /// 事件系统测试器
    /// 用于测试和验证UI与场景交互事件的优先级管理
    /// </summary>
    public class EventSystemTester : MonoBehaviour
    {
        [Header("测试设置")]
        public bool enableTesting = true;
        public bool showDebugGUI = true;
        public bool logEventDetails = true;

        [Header("组件引用")]
        public InputManager inputManager;
        public UIManager uiManager;

        // 测试状态
        private InputEventPriorityManager priorityManager;
        private InputSystemManager inputSystemManager;
        private bool lastFrameWheelInput = false;
        private Vector2 lastMousePosition;
        private InputEventPriorityManager.EventPriority lastEventPriority;

        // 统计数据（简化版）
        private int uiEventCount = 0;
        private int sceneEventCount = 0;
        private int blockedSceneEventCount = 0;

        void Start()
        {
            if (!enableTesting) return;

            InitializeTestComponents();
            Logging.LogInfo("EventSystemTester", "事件系统测试器已启动");
        }

        void Update()
        {
            if (!enableTesting) return;

            TestEventPriority();
            TestWheelEventBlocking();
        }

        void OnGUI()
        {
            if (!showDebugGUI || !enableTesting) return;

            DrawDebugGUI();
        }

        #region 初始化方法
        private void InitializeTestComponents()
        {
            // 获取组件引用
            if (inputManager == null)
                inputManager = FindFirstObjectByType<InputManager>();

            if (uiManager == null)
                uiManager = FindFirstObjectByType<UIManager>();

            priorityManager = InputEventPriorityManager.Instance;
            inputSystemManager = FindFirstObjectByType<InputSystemManager>();

            // 验证组件
            if (priorityManager == null)
                Logging.LogWarning("EventSystemTester", "未找到InputEventPriorityManager");

            if (inputSystemManager == null)
                Logging.LogWarning("EventSystemTester", "未找到EventSystemManager");

            if (inputManager == null)
                Logging.LogWarning("EventSystemTester", "未找到InputManager");

            if (uiManager == null)
                Logging.LogWarning("EventSystemTester", "未找到UIManager");
        }
        #endregion

        #region 测试方法
        private void TestEventPriority()
        {
            if (priorityManager == null) return;

            Vector2 currentMousePosition = Mouse.current.position.ReadValue();

            // 只在鼠标位置改变时更新
            if (Vector2.Distance(currentMousePosition, lastMousePosition) > 1f)
            {
                var currentPriority = priorityManager.GetEventPriority(currentMousePosition);

                if (currentPriority != lastEventPriority)
                {
                    UpdateEventPriorityStats(currentPriority);

                    if (logEventDetails)
                    {
                        Logging.LogInfo("EventSystemTester",
                            $"事件优先级变化: {GetPriorityName(lastEventPriority)} -> {GetPriorityName(currentPriority)}");
                    }

                    lastEventPriority = currentPriority;
                }

                lastMousePosition = currentMousePosition;
            }
        }

        private void TestWheelEventBlocking()
        {
            bool currentWheelInput = Mathf.Abs(Mouse.current.scroll.ReadValue().y) > 0.1f;

            if (currentWheelInput && !lastFrameWheelInput)
            {
                // 检测到滚轮输入
                Vector2 mousePosition = Mouse.current.position.ReadValue();

                if (priorityManager != null)
                {
                    var priority = priorityManager.GetEventPriority(mousePosition);
                    bool shouldBlock = priorityManager.ShouldBlockSceneEvents(mousePosition);

                    if (shouldBlock)
                    {
                        blockedSceneEventCount++;
                        if (logEventDetails)
                        {
                            Logging.LogInfo("EventSystemTester",
                                $"滚轮事件被阻断 - 优先级: {GetPriorityName(priority)}, 位置: {mousePosition}");
                        }
                    }
                    else
                    {
                        if (logEventDetails)
                        {
                            Logging.LogInfo("EventSystemTester",
                                $"滚轮事件允许传播 - 优先级: {GetPriorityName(priority)}, 位置: {mousePosition}");
                        }
                    }
                }
            }

            lastFrameWheelInput = currentWheelInput;
        }

        private void UpdateEventPriorityStats(InputEventPriorityManager.EventPriority priority)
        {
            switch (priority)
            {
                case InputEventPriorityManager.EventPriority.UI_Event:
                    uiEventCount++;
                    break;
                case InputEventPriorityManager.EventPriority.Scene_Event:
                    sceneEventCount++;
                    break;
            }
        }
        #endregion

        #region GUI绘制
        private void DrawDebugGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 400, 300));
            GUILayout.BeginVertical("box");

            GUILayout.Label("事件系统测试器", GUI.skin.label);
            GUILayout.Space(10);

            // 当前状态
            GUILayout.Label("当前状态:", GUI.skin.label);
            Vector2 mousePos = Mouse.current.position.ReadValue();
            GUILayout.Label($"鼠标位置: ({mousePos.x:F0}, {mousePos.y:F0})");

            if (priorityManager != null)
            {
                var priority = priorityManager.GetEventPriority(mousePos);
                GUILayout.Label($"事件优先级: {GetPriorityName(priority)}");

                bool shouldBlock = priorityManager.ShouldBlockSceneEvents(mousePos);
                GUILayout.Label($"阻断场景事件: {(shouldBlock ? "是" : "否")}");

                bool allowScene = priorityManager.AllowSceneInteraction(mousePos);
                GUILayout.Label($"允许场景交互: {(allowScene ? "是" : "否")}");
            }

            GUILayout.Space(10);

            // 统计数据
            GUILayout.Label("统计数据:", GUI.skin.label);
            GUILayout.Label($"UI事件检测次数: {uiEventCount}");
            GUILayout.Label($"场景事件检测次数: {sceneEventCount}");
            GUILayout.Label($"被阻断的场景事件: {blockedSceneEventCount}");

            GUILayout.Space(10);

            // 控制按钮
            if (GUILayout.Button("重置统计"))
            {
                ResetStats();
            }

            if (GUILayout.Button("切换详细日志"))
            {
                logEventDetails = !logEventDetails;
                Logging.LogInfo("EventSystemTester", $"详细日志: {(logEventDetails ? "开启" : "关闭")}");
            }

            if (priorityManager != null)
            {
                if (GUILayout.Button("切换优先级管理器GUI显示"))
                {
                    priorityManager.SetGUIDisplay(!priorityManager.showEventPriorityInGUI);
                }

                if (GUILayout.Button("切换优先级管理器调试日志"))
                {
                    priorityManager.SetDebugLogging(!priorityManager.enableDebugLogging);
                }
            }

            GUILayout.EndVertical();
            GUILayout.EndArea();
        }
        #endregion

        #region 辅助方法
        private string GetPriorityName(InputEventPriorityManager.EventPriority priority)
        {
            return priority switch
            {
                InputEventPriorityManager.EventPriority.UI_Event => "UI事件",
                InputEventPriorityManager.EventPriority.Scene_Event => "场景事件",
                _ => "未知"
            };
        }

        private void ResetStats()
        {
            uiEventCount = 0;
            sceneEventCount = 0;
            blockedSceneEventCount = 0;
            Logging.LogInfo("EventSystemTester", "统计数据已重置");
        }
        #endregion

        #region 公共API
        /// <summary>
        /// 启用/禁用测试
        /// </summary>
        public void SetTestingEnabled(bool enabled)
        {
            enableTesting = enabled;
            Logging.LogInfo("EventSystemTester", $"测试模式: {(enabled ? "启用" : "禁用")}");
        }

        /// <summary>
        /// 启用/禁用GUI显示
        /// </summary>
        public void SetGUIEnabled(bool enabled)
        {
            showDebugGUI = enabled;
        }

        /// <summary>
        /// 启用/禁用详细日志
        /// </summary>
        public void SetDetailedLogging(bool enabled)
        {
            logEventDetails = enabled;
        }

        /// <summary>
        /// 获取当前统计数据
        /// </summary>
        public (int ui, int scene, int blocked) GetStats()
        {
            return (uiEventCount, sceneEventCount, blockedSceneEventCount);
        }
        #endregion
    }
}
