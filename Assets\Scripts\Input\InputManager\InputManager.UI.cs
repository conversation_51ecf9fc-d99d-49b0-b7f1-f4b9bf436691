using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem;
using UnityEngine.UIElements;

public partial class InputManager : MonoBehaviour
{
  #region UI检测和EventSystem管理
  // EventSystem管理方法
  private bool IsPointerOverUI()
  {
    if (useEventSystemManager && inputSystemManager != null)
    {
      return inputSystemManager.IsPointerOverUI();
    }

    // 回退到默认检测
    if (EventSystem.current != null)
    {
      return EventSystem.current.IsPointerOverGameObject();
    }

    return false;
  }

  // 缓存UIDocument列表以避免频繁查找
  private UIDocument[] cachedUIDocuments;
  private float lastUIDocumentCacheTime = 0f;
  private const float UI_DOCUMENT_CACHE_DURATION = 1f; // 1秒更新一次缓存

  /// <summary>
  /// 检查是否有任何TextField获得焦点
  /// 当有TextField获得焦点时，应该暂停键盘移动控制
  /// </summary>
  /// <returns>true表示有TextField获得焦点</returns>
  private bool IsAnyTextFieldFocused()
  {
    // 优化：使用缓存的UIDocument列表
    if (cachedUIDocuments == null || Time.time - lastUIDocumentCacheTime > UI_DOCUMENT_CACHE_DURATION)
    {
      cachedUIDocuments = FindObjectsByType<UIDocument>(FindObjectsSortMode.None);
      lastUIDocumentCacheTime = Time.time;
    }

    // 方法1：检查所有UIDocument中的TextField
    foreach (var uiDocument in cachedUIDocuments)
    {
      if (uiDocument != null && uiDocument.rootVisualElement != null)
      {
        var textFields = uiDocument.rootVisualElement.Query<TextField>().ToList();
        foreach (var textField in textFields)
        {
          if (textField.focusController != null &&
              textField.focusController.focusedElement == textField)
          {
            return true;
          }
        }
      }
    }

    // 方法2：检查EventSystem当前选中的GameObject是否包含TextField相关组件
    if (EventSystem.current != null && EventSystem.current.currentSelectedGameObject != null)
    {
      var selectedObject = EventSystem.current.currentSelectedGameObject;
      // 检查是否是Legacy UI的InputField
      if (selectedObject.GetComponent<UnityEngine.UI.InputField>() != null ||
          selectedObject.GetComponent<TMPro.TMP_InputField>() != null)
      {
        return true;
      }
    }

    return false;
  }

  // 上一帧的TextField焦点状态，用于检测状态变化
  private bool wasTextFieldFocusedLastFrame = false;

  /// <summary>
  /// 检查是否应该暂停键盘移动控制
  /// 综合考虑UI模式和TextField焦点状态
  /// </summary>
  /// <returns>true表示应该暂停键盘移动控制</returns>
  private bool ShouldPauseKeyboardMovement()
  {
    bool isTextFieldFocused = IsAnyTextFieldFocused();

    // 检测TextField焦点状态变化
    if (isTextFieldFocused != wasTextFieldFocusedLastFrame)
    {
      if (isTextFieldFocused)
      {
        // TextField获得焦点，临时禁用Player输入
        TemporarilyDisablePlayerInput();
      }
      else
      {
        // TextField失去焦点，恢复Player输入
        RestorePlayerInput();
      }

      wasTextFieldFocusedLastFrame = isTextFieldFocused;
    }

    return isUIActive || isTextFieldFocused;
  }
  #endregion

  #region UI显示
  // 在UI上显示当前输入状态
  void OnGUI()
  {
    // 检查是否启用调试UI显示
    if (!showDebugUI)
    {
      return;
    }
    GUIStyle style = new GUIStyle(GUI.skin.label);
    style.fontSize = 14;
    style.normal.textColor = Color.white;

    // 计算左下角位置
    float screenHeight = Screen.height;
    float yOffset = screenHeight - 160; // 从底部向上160像素开始

    string inputMode = isUIActive ? "UI模式" : "视角控制模式";
    GUI.Label(new Rect(10, yOffset, 200, 25), $"输入模式: {inputMode}", style);

    // 显示TextField焦点状态
    bool isTextFieldFocused = IsAnyTextFieldFocused();
    if (isTextFieldFocused)
    {
      bool isPlayerInputDisabled = IsPlayerInputTemporarilyDisabled();
      string playerInputStatus = isPlayerInputDisabled ? "已禁用" : "仍启用";
      GUI.Label(new Rect(10, yOffset + 20, 350, 25), $"TextField焦点: 已获得 (Player ActionMap: {playerInputStatus})", style);
      yOffset += 20; // 调整后续显示位置
    }

    if (!isUIActive)
    {
      GUI.Label(new Rect(10, yOffset + 20, 300, 25), "鼠标中键拖动: 旋转视角", style);
      GUI.Label(new Rect(10, yOffset + 40, 300, 25), "鼠标右键拖动: 平移视角", style);
      GUI.Label(new Rect(10, yOffset + 60, 300, 25), "滚轮: 缩放视角", style);
      GUI.Label(new Rect(10, yOffset + 80, 300, 25), "Ctrl + 中键拖动: 平滑缩放", style);
      GUI.Label(new Rect(10, yOffset + 100, 300, 25), "WASD: 相机平移, QE: 上下移动", style);
      GUI.Label(new Rect(10, yOffset + 120, 300, 25), "Shift + 移动键: 加速移动", style);
      GUI.Label(new Rect(10, yOffset + 140, 300, 25), "Tab: 切换到UI模式", style);

      // 显示当前键盘移动状态
      if (isKeyboardMoving)
      {
        bool shiftPressed = Keyboard.current.leftShiftKey.isPressed || Keyboard.current.rightShiftKey.isPressed;
        string speedStatus = shiftPressed ? $"加速移动中 ({keyboardSpeedMultiplier}倍)" : "正常移动";
        GUI.Label(new Rect(10, yOffset + 160, 400, 25), $"键盘移动中 - {speedStatus}", style);
      }

      if (enableMouseWrapping)
      {
        int wrapYOffset = isKeyboardMoving ? 180 : 160;
        GUI.Label(new Rect(10, yOffset + wrapYOffset, 350, 25), "鼠标包裹: 旋转时到达边缘自动跳转", style);
      }

      if (enableSelection)
      {
        int baseOffset = isKeyboardMoving ? 180 : 160;
        int selectionYOffset = enableMouseWrapping ? baseOffset + 20 : baseOffset;
        GUI.Label(new Rect(10, yOffset + selectionYOffset, 300, 25), "左键: 选择物体", style);
        GUI.Label(new Rect(10, yOffset + selectionYOffset + 20, 300, 25), "左键拖拽: 框选物体", style);
        GUI.Label(new Rect(10, yOffset + selectionYOffset + 40, 300, 25), "Ctrl + 左键: 多选", style);
      }

      // 显示当前操作状态
      string statusText = "状态: ";
      if (isKeyboardMoving)
      {
        bool shiftPressed = Keyboard.current.leftShiftKey.isPressed || Keyboard.current.rightShiftKey.isPressed;
        statusText += shiftPressed ? $"键盘移动中 (加速 {keyboardSpeedMultiplier}倍)" : "键盘移动中";
      }
      else if (isRotating)
        statusText += "旋转视角中";
      else if (isPanning)
        statusText += "平移视角中";
      else if (isSmoothZooming)
        statusText += "平滑缩放中";
      else if (isZoomEasing)
        statusText += "缩放缓动中";
      else if (isSelecting)
        statusText += "框选中";
      else
        statusText += "待机";

      int statusYOffset = enableSelection ? (enableMouseWrapping ? 200 : 180) : (enableMouseWrapping ? 140 : 120);
      GUI.Label(new Rect(10, yOffset + statusYOffset, 200, 25), statusText, style);

      // 显示选择信息
      if (enableSelection && selectionManager != null)
      {
        int selectedCount = selectionManager.GetSelectedObjects().Count;
        int selectionInfoYOffset = enableMouseWrapping ? 220 : 200;
        GUI.Label(new Rect(10, yOffset + selectionInfoYOffset, 200, 25), $"已选择: {selectedCount} 个物体", style);
      }
    }
    else
    {
      GUI.Label(new Rect(10, yOffset + 20, 300, 25), "方向键: UI导航", style);
      GUI.Label(new Rect(10, yOffset + 40, 300, 25), "Tab: 切换到视角控制模式", style);
    }
  }
  #endregion
}
