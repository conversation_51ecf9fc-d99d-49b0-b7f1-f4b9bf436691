using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.Events.Core;
using BlastingDesign.Events;

namespace BlastingDesign.UI.Core
{
    /// <summary>
    /// 轻量级UI元素基类 - 用于[UxmlElement]组件
    /// 结合了AreaBtn的自包含设计和当前架构的事件系统
    /// </summary>
    public abstract class UIElementBase : VisualElement
    {
        [Header("Element Settings")]
        protected string elementName;

        [Header("Event Blocking Settings")]
        protected bool enableEventBlocking = true; // 是否启用事件阻断
        protected bool debugEventBlocking = false; // 是否启用事件阻断调试

        // 资源路径 - 子类需要重写
        protected abstract string TemplatePath { get; }
        protected virtual string StylePath => TemplatePath; // 默认与模板路径相同

        // UI资源
        protected VisualTreeAsset elementTemplate;
        protected StyleSheet elementStyleSheet;
        // 旧事件系统引用已移除
        protected IEventBus eventBus; // 新事件系统引用
        protected bool isInitialized = false;

        // 属性
        public string ElementName => elementName;
        public bool IsInitialized => isInitialized;

        public UIElementBase()
        {
            // 设置元素名称
            elementName = GetType().Name;

            // 旧事件系统引用已移除

            // 获取新事件系统引用
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager != null)
            {
                eventBus = eventSystemManager.EventBus;
            }

            // 注意：不在构造函数中立即初始化，避免时序问题
            // 初始化将在适当的时机通过Initialize()方法调用
        }

        /// <summary>
        /// 显式初始化元素 - 在适当的时机调用
        /// </summary>
        public virtual void Initialize()
        {
            if (isInitialized)
            {
                Logging.LogWarning("UIElementBase", $"{elementName} 已经初始化，跳过重复初始化");
                return;
            }

            Logging.LogInfo("UIElementBase", $"开始初始化 {elementName}");
            InitializeElement();
        }

        /// <summary>
        /// 内部初始化方法
        /// </summary>
        protected virtual void InitializeElement()
        {
            try
            {
                // 重新获取事件系统引用（确保在初始化时获取最新的引用）
                EnsureEventSystemReferences();

                // 加载资源
                if (!LoadResources())
                {
                    Logging.LogError("UIElementBase", "资源加载失败");

                    return;
                }

                // 创建UI结构
                if (!CreateUIStructure())
                {
                    Logging.LogError("UIElementBase", "UI结构创建失败");
                    return;
                }

                // 缓存UI元素
                try
                {
                    CacheUIElements();
                }
                catch (NullReferenceException ex)
                {
                    Logging.LogError("UIElementBase", $"缓存UI元素时发生空引用异常: {ex.Message}");
                    return;
                }
                catch (Exception ex)
                {
                    Logging.LogError("UIElementBase", $"缓存UI元素时发生异常: {ex.Message}");
                    return;
                }
                Logging.LogInfo("UIElementBase", "UI元素缓存完成");

                // 设置事件监听器
                SetupEventListeners();
                Logging.LogInfo("UIElementBase", "事件监听器设置完成");

                // 设置事件阻断
                SetupEventBlocking();
                Logging.LogInfo("UIElementBase", "事件阻断设置完成");

                // 初始化数据
                InitializeData();
                Logging.LogInfo("UIElementBase", "数据初始化完成");

                isInitialized = true;
                Logging.LogInfo("UIElementBase", $"{elementName} 初始化完成");
            }
            catch (Exception ex)
            {
                Logging.LogError("UIElementBase", $"初始化过程中发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 加载资源（模板和样式）
        /// </summary>
        protected virtual bool LoadResources()
        {
            // 加载UXML模板
            if (!LoadTemplate())
            {
                return false;
            }

            // 加载USS样式
            LoadStyleSheet();

            return true;
        }

        /// <summary>
        /// 加载UXML模板资源
        /// </summary>
        protected virtual bool LoadTemplate()
        {
            if (string.IsNullOrEmpty(TemplatePath))
            {
                Logging.LogError("UIElementBase", "模板路径未设置");
                return false;
            }

            elementTemplate = Resources.Load<VisualTreeAsset>($"{TemplatePath}/{elementName}");
            if (elementTemplate == null)
            {
                Logging.LogError("UIElementBase", $"无法加载模板: {TemplatePath}/{elementName}.uxml");
                return false;
            }

            Logging.LogInfo("UIElementBase", $"模板加载成功: {TemplatePath}/{elementName}");
            return true;
        }

        /// <summary>
        /// 加载USS样式资源
        /// </summary>
        protected virtual void LoadStyleSheet()
        {
            if (string.IsNullOrEmpty(StylePath))
            {
                Logging.LogWarning("UIElementBase", "样式路径未设置，跳过样式加载");
                return;
            }

            elementStyleSheet = Resources.Load<StyleSheet>($"{StylePath}/{elementName}");
            if (elementStyleSheet == null)
            {
                Logging.LogError("UIElementBase", $"无法加载样式表: {StylePath}/{elementName}.uss");
                return;
            }

            Logging.LogInfo("UIElementBase", $"样式表加载成功: {StylePath}/{elementName}");
        }

        /// <summary>
        /// 创建UI结构
        /// </summary>
        protected virtual bool CreateUIStructure()
        {
            if (elementTemplate == null)
            {
                Logging.LogError("UIElementBase", "模板为空，无法创建UI结构");
                return false;
            }

            var templateElement = elementTemplate.Instantiate();
            if (templateElement == null)
            {
                Logging.LogError("UIElementBase", "模板实例化失败");
                return false;
            }

            // 应用样式表
            if (elementStyleSheet != null)
            {
                templateElement.styleSheets.Add(elementStyleSheet);
                Logging.LogInfo("UIElementBase", "样式表已应用");
            }

            // 设置样式
            // style.flexGrow = 0;

            // 添加模板内容
            Add(templateElement);

            Logging.LogInfo("UIElementBase", "UI结构创建成功");
            return true;
        }

        /// <summary>
        /// 缓存UI元素引用 - 子类重写
        /// </summary>
        protected abstract void CacheUIElements();

        /// <summary>
        /// 安全地查找UI元素
        /// </summary>
        /// <typeparam name="T">UI元素类型</typeparam>
        /// <param name="name">元素名称</param>
        /// <returns>找到的元素，如果未找到则返回null</returns>
        protected T SafeQuery<T>(string name) where T : VisualElement
        {
            try
            {
                return this.Q<T>(name);
            }
            catch (Exception ex)
            {
                Logging.LogWarning("UIElementBase", $"{elementName} 查找UI元素 '{name}' 失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 延迟初始化 - 在下一帧执行
        /// </summary>
        public void InitializeDelayed()
        {
            if (isInitialized)
            {
                return;
            }

            // 使用Unity的调度器在下一帧执行初始化
            schedule.Execute(() =>
            {
                if (!isInitialized)
                {
                    Initialize();
                }
            });
        }

        /// <summary>
        /// 验证UI结构是否正确加载
        /// </summary>
        protected virtual bool ValidateUIStructure()
        {
            if (childCount == 0)
            {
                Logging.LogWarning("UIElementBase", $"{elementName} 没有子元素，UI结构可能未正确加载");
                return false;
            }

            Logging.LogInfo("UIElementBase", $"{elementName} UI结构验证通过，子元素数量: {childCount}");
            return true;
        }

        /// <summary>
        /// 设置事件监听器 - 子类重写
        /// </summary>
        protected abstract void SetupEventListeners();

        /// <summary>
        /// 设置事件阻断 - 防止UI事件传播到场景交互系统
        /// </summary>
        protected virtual void SetupEventBlocking()
        {
            if (!enableEventBlocking) return;

            try
            {
                // 为当前元素添加滚轮事件阻断
                UIEventBlocker.AddWheelEventBlocker(this, debugEventBlocking, true);

                // 不对ScrollView子元素添加事件阻断，让它们保持正常滚动功能
                // UIEventBlocker.AddEventBlockerForType<ScrollView>(this, debugEventBlocking);

                // 为所有DropdownField子元素添加事件阻断
                UIEventBlocker.AddEventBlockerForType<DropdownField>(this, debugEventBlocking);

                // 为所有Slider子元素添加事件阻断
                UIEventBlocker.AddEventBlockerForType<Slider>(this, debugEventBlocking);

                if (debugEventBlocking)
                {
                    Logging.LogInfo("UIElementBase", $"{elementName} 事件阻断设置完成");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("UIElementBase", $"{elementName} 事件阻断设置失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化数据 - 子类可重写
        /// </summary>
        protected virtual void InitializeData()
        {
            // 默认实现为空，子类可根据需要重写
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public virtual void Cleanup()
        {
            // 移除事件监听器
            RemoveEventListeners();

            // 清理UI元素
            Clear();

            isInitialized = false;
            Logging.LogInfo("UIElementBase", "元素清理完成");
        }

        /// <summary>
        /// 移除事件监听器 - 子类重写
        /// </summary>
        protected abstract void RemoveEventListeners();

        /// <summary>
        /// 触发UI事件系统事件的便捷方法
        /// </summary>
        protected void TriggerEvent(Action eventAction)
        {
            try
            {
                eventAction?.Invoke();
            }
            catch (Exception ex)
            {
                Logging.LogError("UIElementBase", $"触发事件时发生错误: {ex.Message}");
            }
        }

        #region 新事件系统便捷方法

        /// <summary>
        /// 确保事件系统引用是最新的
        /// </summary>
        private void EnsureEventSystemReferences()
        {
            // 重新获取新事件系统引用
            if (eventBus == null)
            {
                var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
                if (eventSystemManager != null)
                {
                    eventBus = eventSystemManager.EventBus;
                    if (eventBus != null)
                    {
                        Logging.LogInfo(elementName, "EventBus引用已更新");
                    }
                    else
                    {
                        Logging.LogWarning(elementName, "EventSystemManager存在但EventBus为null");
                    }
                }
                else
                {
                    Logging.LogWarning(elementName, "EventSystemManager实例不存在");
                }
            }
        }

        /// <summary>
        /// 发布事件到新事件系统
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="eventInstance">事件实例</param>
        protected virtual void PublishEvent<T>(T eventInstance) where T : IEvent
        {
            // 确保EventBus引用是最新的
            if (eventBus == null)
            {
                EnsureEventSystemReferences();
            }

            if (eventBus != null)
            {
                try
                {
                    eventBus.Publish(eventInstance);
                }
                catch (Exception ex)
                {
                    Logging.LogError(elementName, $"发布事件失败: {ex.Message}");
                }
            }
            else
            {
                Logging.LogWarning(elementName, "EventBus未初始化，无法发布事件");

                // 提供调试信息
                var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
                if (eventSystemManager == null)
                {
                    Logging.LogWarning(elementName, "调试信息: EventSystemManager实例不存在，请确保场景中有EventSystemManager组件");
                }
                else
                {
                    Logging.LogWarning(elementName, $"调试信息: EventSystemManager存在，但EventBus为null。EventBus类型: {eventSystemManager.EventBus?.GetType().Name ?? "null"}");
                }
            }
        }

        /// <summary>
        /// 订阅事件
        /// </summary>
        /// <typeparam name="T">事件类型</typeparam>
        /// <param name="handler">事件处理器</param>
        /// <returns>订阅句柄</returns>
        protected virtual IEventSubscription SubscribeEvent<T>(Action<T> handler) where T : IEvent
        {
            // 确保EventBus引用是最新的
            if (eventBus == null)
            {
                EnsureEventSystemReferences();
            }

            if (eventBus != null)
            {
                try
                {
                    return eventBus.Subscribe(handler);
                }
                catch (Exception ex)
                {
                    Logging.LogError(elementName, $"订阅事件失败: {ex.Message}");
                    return null;
                }
            }
            else
            {
                Logging.LogWarning(elementName, "EventBus未初始化，无法订阅事件");
                return null;
            }
        }

        // PublishCompatEvent方法已移除 - 兼容性API不再需要

        #endregion
    }
}
