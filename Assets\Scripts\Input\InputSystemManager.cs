using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.InputSystem.UI;
using UnityEngine.UIElements;
using BlastingDesign.Core.Input;

/// <summary>
/// EventSystem管理器，解决InputSystemUIInputModule与框选功能的冲突
/// </summary>
public class InputSystemManager : MonoBehaviour
{
    [Header("EventSystem设置")]
    public bool autoManageEventSystem = true;
    public bool allowSelectionWhenUIActive = false; // 是否允许在UI激活时进行选择

    [Header("调试信息")]
    public bool showDebugInfo = false;

    private EventSystem eventSystem;
    private InputSystemUIInputModule inputModule;
    private bool isSelectionActive = false;
    private bool originalModuleState = true;
    private bool isFirstPersonMode = false; // 第一人称模式状态

    // UI Toolkit相关（简化版）
    private UIDocument uiDocument;

    // 单例模式
    public static InputSystemManager Instance { get; private set; }

    void Awake()
    {
        // 单例模式
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
            InitializeEventSystem();
        }
        else
        {
            Destroy(gameObject);
        }
    }

    void InitializeEventSystem()
    {
        if (!autoManageEventSystem) return;

        // 查找或创建EventSystem
        eventSystem = FindFirstObjectByType<EventSystem>();
        if (eventSystem == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystem = eventSystemGO.AddComponent<EventSystem>();

            if (showDebugInfo)
                Debug.Log("EventSystemManager: 自动创建了EventSystem");
        }

        // 查找或创建InputSystemUIInputModule
        inputModule = eventSystem.GetComponent<InputSystemUIInputModule>();
        if (inputModule == null)
        {
            inputModule = eventSystem.gameObject.AddComponent<InputSystemUIInputModule>();

            if (showDebugInfo)
                Debug.Log("EventSystemManager: 自动创建了InputSystemUIInputModule");
        }

        originalModuleState = inputModule.enabled;

        // 初始化UI Toolkit相关
        InitializeUIToolkit();

        if (showDebugInfo)
        {
            Debug.Log($"EventSystemManager: 初始化完成");
            Debug.Log($"EventSystem: {eventSystem.name}");
            Debug.Log($"InputModule: {inputModule.GetType().Name}");
        }
    }

    void InitializeUIToolkit()
    {
        // 查找UIDocument组件（简化版）
        uiDocument = FindFirstObjectByType<UIDocument>();
        if (uiDocument != null)
        {
            if (showDebugInfo)
                Debug.Log("EventSystemManager: UI Toolkit初始化完成");
        }
        else if (showDebugInfo)
        {
            Debug.LogWarning("EventSystemManager: 未找到UIDocument组件");
        }
    }

    void Start()
    {
        // 简化版本不需要额外的初始化
    }

    /// <summary>
    /// 开始选择操作，临时调整EventSystem设置
    /// </summary>
    public void BeginSelection()
    {
        if (!autoManageEventSystem || inputModule == null) return;

        isSelectionActive = true;

        // 方案1: 临时禁用InputSystemUIInputModule
        // 这样可以防止UI模块消费鼠标事件
        inputModule.enabled = false;

        if (showDebugInfo)
            Debug.Log("EventSystemManager: 开始选择操作，已禁用UI输入模块");
    }

    /// <summary>
    /// 结束选择操作，恢复EventSystem设置
    /// </summary>
    public void EndSelection()
    {
        if (!autoManageEventSystem || inputModule == null) return;

        isSelectionActive = false;

        // 恢复InputSystemUIInputModule
        inputModule.enabled = originalModuleState;

        if (showDebugInfo)
            Debug.Log("EventSystemManager: 结束选择操作，已恢复UI输入模块");
    }

    /// <summary>
    /// 检查鼠标是否在UI上（支持拖拽持续性）
    /// </summary>
    public bool IsPointerOverUI()
    {
        if (eventSystem == null) return false;

        // 如果处于第一人称模式，始终返回false以避免阻止输入
        if (isFirstPersonMode)
            return false;

        // 如果正在选择且不允许UI时选择，返回false
        if (isSelectionActive && !allowSelectionWhenUIActive)
            return false;

        // **关键修复**: 检查拖拽持续性
        // 如果有拖拽操作正在进行，不应该阻断场景事件，所以返回false
        if (ShouldRespectDragContinuity())
        {
            if (showDebugInfo)
                Debug.Log("EventSystemManager: 拖拽持续性激活，IsPointerOverUI返回false");
            return false;
        }

        // 简化版：直接检查是否在UI上
        return eventSystem.IsPointerOverGameObject();
    }

    /// <summary>
    /// 检查是否应该尊重拖拽持续性或事件源
    /// </summary>
    /// <returns>true表示有拖拽操作正在进行或事件源自场景，应该允许场景事件</returns>
    private bool ShouldRespectDragContinuity()
    {
        // 检查InputEventPriorityManager是否存在
        if (InputEventPriorityManager.Instance == null)
            return false;

        var manager = InputEventPriorityManager.Instance;

        // 优先检查事件源：如果事件源自场景，应该允许场景事件
        if (manager.IsEventSourceDetermined() &&
            manager.GetCurrentEventSource() == InputEventPriorityManager.EventSourceType.Scene)
        {
            return true;
        }

        // 备用检查：如果有拖拽操作正在进行，也允许场景事件
        return manager.IsDragOperationActive();
    }




    /// <summary>
    /// 强制设置InputModule状态
    /// </summary>
    public void SetInputModuleEnabled(bool enabled)
    {
        if (inputModule != null)
        {
            inputModule.enabled = enabled;

            if (showDebugInfo)
                Debug.Log($"EventSystemManager: 手动设置InputModule状态为 {enabled}");
        }
    }

    /// <summary>
    /// 获取当前EventSystem状态信息
    /// </summary>
    public string GetStatusInfo()
    {
        if (eventSystem == null || inputModule == null)
            return "EventSystem未初始化";

        return $"EventSystem: {eventSystem.enabled}, InputModule: {inputModule.enabled}, Selection: {isSelectionActive}";
    }

    void OnDestroy()
    {
        if (Instance == this)
        {
            Instance = null;
        }
    }

    // 调试用的OnGUI显示
    void OnGUI()
    {
        if (!showDebugInfo) return;

        GUIStyle style = new GUIStyle(GUI.skin.label);
        style.fontSize = 12;
        style.normal.textColor = Color.yellow;

        float yPos = 10;
        GUI.Label(new Rect(10, yPos, 400, 20), $"EventSystem状态: {GetStatusInfo()}", style);

        yPos += 20;
        GUI.Label(new Rect(10, yPos, 400, 20), $"鼠标在UI上: {IsPointerOverUI()}", style);

        yPos += 20;
        bool isOverGameObject = eventSystem != null && eventSystem.IsPointerOverGameObject();
        GUI.Label(new Rect(10, yPos, 400, 20), $"原始UI检测: {isOverGameObject}", style);

        yPos += 20;
        bool isDragActive = ShouldRespectDragContinuity();
        GUI.Label(new Rect(10, yPos, 400, 20), $"事件源/拖拽持续性: {(isDragActive ? "激活" : "未激活")}", style);

        if (InputEventPriorityManager.Instance != null)
        {
            yPos += 20;
            var eventSource = InputEventPriorityManager.Instance.GetCurrentEventSource();
            bool eventSourceDetermined = InputEventPriorityManager.Instance.IsEventSourceDetermined();
            GUI.Label(new Rect(10, yPos, 400, 20), $"事件源: {(eventSourceDetermined ? eventSource.ToString() : "未确定")}", style);
        }

        yPos += 20;
        GUI.Label(new Rect(10, yPos, 400, 20), $"第一人称模式: {isFirstPersonMode}", style);
    }

    #region 第一人称模式支持
    /// <summary>
    /// 设置第一人称模式状态
    /// 在第一人称模式下，阻止所有UI事件处理
    /// </summary>
    /// <param name="enabled">是否启用第一人称模式</param>
    public void SetFirstPersonMode(bool enabled)
    {
        isFirstPersonMode = enabled;

        if (showDebugInfo)
        {
            Debug.Log($"EventSystemManager: 第一人称模式 {(enabled ? "启用" : "禁用")}");
        }

        // 在第一人称模式下，临时禁用InputModule以阻止UI事件
        if (inputModule != null)
        {
            if (enabled)
            {
                // 保存原始状态并禁用
                originalModuleState = inputModule.enabled;
                inputModule.enabled = false;
            }
            else
            {
                // 恢复原始状态
                inputModule.enabled = originalModuleState;
            }
        }
    }

    /// <summary>
    /// 获取第一人称模式状态
    /// </summary>
    /// <returns>是否处于第一人称模式</returns>
    public bool IsFirstPersonMode()
    {
        return isFirstPersonMode;
    }
    #endregion
}
