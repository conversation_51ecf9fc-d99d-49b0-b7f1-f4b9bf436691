using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Components;
using BlastingDesign.UI.Core;
using BlastingDesign.Events.Core;
using BlastingDesign.Events;

namespace BlastingDesign.UI.Config
{
    /// <summary>
    /// UI回调系统 - 处理配置化的菜单项和工具按钮回调
    /// 支持字符串名称映射到具体的回调方法
    /// </summary>
    public class UICallbackSystem : MonoBehaviour
    {
        private static UICallbackSystem instance;
        public static UICallbackSystem Instance
        {
            get
            {
                if (instance == null)
                {
                    instance = FindFirstObjectByType<UICallbackSystem>();
                    if (instance == null)
                    {
                        var go = new GameObject("UICallbackSystem");
                        instance = go.AddComponent<UICallbackSystem>();
                        DontDestroyOnLoad(go);
                    }
                }
                return instance;
            }
        }

        // 回调委托定义
        public delegate void UICallback(string[] parameters);
        public delegate bool UICallbackWithReturn(string[] parameters);

        // 回调注册表
        private readonly Dictionary<string, UICallback> callbacks = new();
        private readonly Dictionary<string, UICallbackWithReturn> callbacksWithReturn = new();

        // 内置回调处理器
        private Dictionary<string, Action<string[]>> builtinCallbacks;

        private void Awake()
        {
            if (instance == null)
            {
                instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeBuiltinCallbacks();
            }
            else if (instance != this)
            {
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// 初始化内置回调
        /// </summary>
        private void InitializeBuiltinCallbacks()
        {
            builtinCallbacks = new Dictionary<string, Action<string[]>>
            {
                // 文件菜单回调
                { "NewScene", HandleNewScene },
                { "OpenScene", HandleOpenScene },
                { "SaveScene", HandleSaveScene },
                { "Import", HandleImport },
                { "Export", HandleExport },
                { "Setting", HandleSetting },
                
                // 编辑菜单回调
                { "Undo", HandleUndo },
                { "Redo", HandleRedo },
                { "Copy", HandleCopy },
                { "Paste", HandlePaste },
                
                // 视图菜单回调
                { "ZoomIn", HandleZoomIn },
                { "ZoomOut", HandleZoomOut },
                { "FitView", HandleFitView },
                
                // 工具菜单回调
                { "SelectMeasureTool", HandleSelectMeasureTool },
                { "SelectAnnotationTool", HandleSelectAnnotationTool },
                { "SelectScreenshotTool", HandleSelectScreenshotTool },
                { "ShowDatabaseConnectionTest", HandleShowDatabaseConnectionTest },
                { "ShowComponentLibrary", HandleShowComponentLibrary },
                
                // 帮助菜单回调
                { "ShowUserManual", HandleShowUserManual },
                { "ShowShortcuts", HandleShowShortcuts },
                { "ShowAbout", HandleShowAbout },
                
                // 工具栏回调
                { "SelectTool", HandleSelectTool },
                { "MoveTool", HandleMoveTool },
                { "RotateTool", HandleRotateTool },
                { "ScaleTool", HandleScaleTool },
                { "PanTool", HandlePanTool },
                { "ZoomTool", HandleZoomTool },
                { "TogglePlay", HandleTogglePlay }
            };

            // 注册内置回调到系统
            foreach (var kvp in builtinCallbacks)
            {
                RegisterCallback(kvp.Key, (parameters) => kvp.Value(parameters));
            }
        }

        /// <summary>
        /// 注册回调
        /// </summary>
        public void RegisterCallback(string callbackName, UICallback callback)
        {
            if (string.IsNullOrEmpty(callbackName) || callback == null)
            {
                Logging.LogWarning("UICallbackSystem", "无效的回调注册参数");
                return;
            }

            callbacks[callbackName] = callback;
            Logging.LogInfo("UICallbackSystem", $"注册回调: {callbackName}");
        }

        /// <summary>
        /// 注册带返回值的回调
        /// </summary>
        public void RegisterCallbackWithReturn(string callbackName, UICallbackWithReturn callback)
        {
            if (string.IsNullOrEmpty(callbackName) || callback == null)
            {
                Logging.LogWarning("UICallbackSystem", "无效的回调注册参数");
                return;
            }

            callbacksWithReturn[callbackName] = callback;
            Logging.LogInfo("UICallbackSystem", $"注册带返回值回调: {callbackName}");
        }

        /// <summary>
        /// 取消注册回调
        /// </summary>
        public void UnregisterCallback(string callbackName)
        {
            if (callbacks.ContainsKey(callbackName))
            {
                callbacks.Remove(callbackName);
                Logging.LogInfo("UICallbackSystem", $"取消注册回调: {callbackName}");
            }

            if (callbacksWithReturn.ContainsKey(callbackName))
            {
                callbacksWithReturn.Remove(callbackName);
                Logging.LogInfo("UICallbackSystem", $"取消注册带返回值回调: {callbackName}");
            }
        }

        /// <summary>
        /// 执行回调
        /// </summary>
        public bool ExecuteCallback(string callbackName, string[] parameters = null)
        {
            if (string.IsNullOrEmpty(callbackName))
            {
                Logging.LogWarning("UICallbackSystem", "回调名称为空");
                return false;
            }

            parameters ??= new string[0];

            try
            {
                if (callbacks.ContainsKey(callbackName))
                {
                    callbacks[callbackName](parameters);
                    Logging.LogInfo("UICallbackSystem", $"执行回调: {callbackName}");
                    return true;
                }
                else
                {
                    Logging.LogWarning("UICallbackSystem", $"未找到回调: {callbackName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("UICallbackSystem", $"执行回调时发生错误 {callbackName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 执行带返回值的回调
        /// </summary>
        public bool ExecuteCallbackWithReturn(string callbackName, string[] parameters = null)
        {
            if (string.IsNullOrEmpty(callbackName))
            {
                Logging.LogWarning("UICallbackSystem", "回调名称为空");
                return false;
            }

            parameters ??= new string[0];

            try
            {
                if (callbacksWithReturn.ContainsKey(callbackName))
                {
                    bool result = callbacksWithReturn[callbackName](parameters);
                    Logging.LogInfo("UICallbackSystem", $"执行带返回值回调: {callbackName}, 结果: {result}");
                    return result;
                }
                else
                {
                    Logging.LogWarning("UICallbackSystem", $"未找到带返回值回调: {callbackName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("UICallbackSystem", $"执行带返回值回调时发生错误 {callbackName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查回调是否存在
        /// </summary>
        public bool HasCallback(string callbackName)
        {
            return callbacks.ContainsKey(callbackName) || callbacksWithReturn.ContainsKey(callbackName);
        }

        /// <summary>
        /// 获取所有已注册的回调名称
        /// </summary>
        public string[] GetRegisteredCallbacks()
        {
            var allCallbacks = new List<string>();
            allCallbacks.AddRange(callbacks.Keys);
            allCallbacks.AddRange(callbacksWithReturn.Keys);
            return allCallbacks.ToArray();
        }

        #region 事件发布辅助方法

        /// <summary>
        /// 发布状态消息事件
        /// </summary>
        private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "UICallbackSystem"));
            }
            else
            {
                Logging.LogWarning("UICallbackSystem", "EventSystemManager未初始化，无法发布状态消息事件");
            }
        }

        #endregion

        #region 内置回调处理方法

        // 文件菜单回调
        private void HandleNewScene(string[] parameters) => Debug.Log("新建场景");
        private void HandleOpenScene(string[] parameters) => Debug.Log("打开场景");
        private void HandleSaveScene(string[] parameters) => Debug.Log("保存场景");
        private void HandleImport(string[] parameters) => Debug.Log("导入");
        private void HandleExport(string[] parameters) => Debug.Log("导出");

        /// <summary>
        /// 处理设置菜单回调 - 打开设置模态窗口
        /// </summary>
        private void HandleSetting(string[] parameters)
        {
            try
            {
                // 检查是否已存在设置窗口
                var existingWindow = ModalWindowManager.Instance != null ? ModalWindowManager.Instance.FindWindow("Settings") : null;
                if (existingWindow != null)
                {
                    // 如果已存在，将其移到前台
                    existingWindow.BringToFront();
                    Logging.LogInfo("UICallbackSystem", "设置窗口已存在，移到前台");
                    return;
                }

                // 创建新的设置窗口
                var settingsWindow = ModalWindowManager.Instance != null ? ModalWindowManager.Instance.CreateSettingsWindow() : null;
                if (settingsWindow != null)
                {
                    Logging.LogInfo("UICallbackSystem", "设置窗口创建成功");
                    PublishStatusMessage("设置窗口已打开");
                }
                else
                {
                    Logging.LogError("UICallbackSystem", "无法创建设置窗口");
                    PublishStatusMessage("设置窗口创建失败", StatusMessageType.Error);
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("UICallbackSystem", $"打开设置窗口时发生错误: {ex.Message}");
                PublishStatusMessage("设置窗口打开失败", StatusMessageType.Error);
            }
        }

        // 编辑菜单回调
        private void HandleUndo(string[] parameters) => Debug.Log("撤销");
        private void HandleRedo(string[] parameters) => Debug.Log("重做");
        private void HandleCopy(string[] parameters) => Debug.Log("复制");
        private void HandlePaste(string[] parameters) => Debug.Log("粘贴");

        // 视图菜单回调
        private void HandleZoomIn(string[] parameters) => Debug.Log("放大");
        private void HandleZoomOut(string[] parameters) => Debug.Log("缩小");
        private void HandleFitView(string[] parameters) => Debug.Log("适应视图");

        // 工具菜单回调
        private void HandleSelectMeasureTool(string[] parameters) => Debug.Log("选择测量工具");
        private void HandleSelectAnnotationTool(string[] parameters) => Debug.Log("选择标注工具");
        private void HandleSelectScreenshotTool(string[] parameters) => Debug.Log("选择截图工具");

        /// <summary>
        /// 处理数据库连接测试菜单回调 - 打开数据库连接测试窗口
        /// </summary>
        private void HandleShowDatabaseConnectionTest(string[] parameters)
        {
            try
            {
                // 每次都创建新的窗口，确保状态干净
                var windowObject = new GameObject("DatabaseConnectionWindow");
                var databaseWindow = windowObject.AddComponent<DatabaseConnectionWindow>();

                // 延迟显示窗口，确保组件完全初始化
                windowObject.GetComponent<MonoBehaviour>().StartCoroutine(ShowWindowAfterDelay(databaseWindow));

                Logging.LogInfo("UICallbackSystem", "数据库连接测试窗口创建成功");
                PublishStatusMessage("数据库连接测试窗口已打开");
            }
            catch (Exception ex)
            {
                Logging.LogError("UICallbackSystem", $"打开数据库连接测试窗口时发生错误: {ex.Message}");
                PublishStatusMessage("数据库连接测试窗口打开失败", StatusMessageType.Error);
            }
        }

        /// <summary>
        /// 延迟显示窗口的协程
        /// </summary>
        private System.Collections.IEnumerator ShowWindowAfterDelay(DatabaseConnectionWindow window)
        {
            // 等待一帧，确保组件完全初始化
            yield return null;

            if (window != null)
            {
                window.ShowWindow();
            }
        }

        /// <summary>
        /// 处理组件库菜单回调 - 打开DaisyUI组件库窗口
        /// </summary>
        private void HandleShowComponentLibrary(string[] parameters)
        {
            try
            {
                // 查找或创建组件库窗口组件
                var componentLibraryWindow = FindFirstObjectByType<BlastingDesign.UI.Components.ComponentLibraryWindow>();

                if (componentLibraryWindow == null)
                {
                    // 创建新的组件库窗口组件
                    var windowObject = new GameObject("ComponentLibraryWindow");
                    componentLibraryWindow = windowObject.AddComponent<BlastingDesign.UI.Components.ComponentLibraryWindow>();

                    Logging.LogInfo("UICallbackSystem", "组件库窗口组件创建成功");
                }

                // 显示窗口
                componentLibraryWindow.ShowWindow();

                Logging.LogInfo("UICallbackSystem", "组件库窗口显示成功");
                PublishStatusMessage("DaisyUI组件库已打开");
            }
            catch (Exception ex)
            {
                Logging.LogError("UICallbackSystem", $"打开组件库窗口时发生错误: {ex.Message}");
                PublishStatusMessage("组件库窗口打开失败", StatusMessageType.Error);
            }
        }

        // 帮助菜单回调
        private void HandleShowUserManual(string[] parameters) => Debug.Log("显示用户手册");
        private void HandleShowShortcuts(string[] parameters) => Debug.Log("显示快捷键");
        private void HandleShowAbout(string[] parameters) => Debug.Log("显示关于");

        // 工具栏回调
        private void HandleSelectTool(string[] parameters) => Debug.Log("选择工具");
        private void HandleMoveTool(string[] parameters) => Debug.Log("移动工具");
        private void HandleRotateTool(string[] parameters) => Debug.Log("旋转工具");
        private void HandleScaleTool(string[] parameters) => Debug.Log("缩放工具");
        private void HandlePanTool(string[] parameters) => Debug.Log("平移工具");
        private void HandleZoomTool(string[] parameters) => Debug.Log("缩放工具");
        private void HandleTogglePlay(string[] parameters) => Debug.Log("切换播放");

        #endregion
    }
}
