using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using System.Collections.Generic;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 右侧面板组件 - 属性编辑器（轻量级架构）
    /// </summary>
    [UxmlElement]
    public partial class RightPanel : UIElementBase, ICollapsible, IResizable, IPropertyEditor
    {
        // 资源路径
        protected override string TemplatePath => "UI/RightPanel";

        [Header("Panel Settings")]
        private float defaultWidth = 300f;
        private float minWidth = 200f;
        private float maxWidth = 500f;
        private bool isCollapsed = false;
        private bool isLocked = false;

        // UI元素引用
        private Button collapseButton;
        private Button lockButton;
        private Button settingsButton;
        private Toggle objectActiveToggle;
        private TextField objectNameField;
        private Button objectIconButton;
        private DropdownField tagDropdown;
        private DropdownField layerDropdown;
        private ScrollView propertiesScroll;
        private VisualElement propertiesContainer;
        private VisualElement emptyState;
        private Button addComponentButton;
        private Button componentMenuButton;
        private VisualElement componentDropdown;
        private VisualElement resizeHandle;

        // 属性字段引用
        private Vector3Field positionField;
        private Vector3Field rotationField;
        private Vector3Field scaleField;
        private CustomColorField colorField;
        private Slider alphaSlider;
        private CustomObjectField materialField;
        private Toggle castShadowsToggle;
        private Toggle receiveShadowsToggle;

        // 物理属性字段
        private FloatField massField;
        private FloatField dragField;
        private FloatField angularDragField;
        private Toggle useGravityToggle;
        private Toggle isKinematicToggle;

        // 碰撞属性字段
        private DropdownField colliderTypeDropdown;
        private Toggle isTriggerToggle;
        private Vector3Field colliderCenterField;
        private Vector3Field colliderSizeField;

        // 数据和状态
        private object currentTarget;
        private bool isDragging = false;
        private float currentWidth;
        private Dictionary<string, Foldout> propertyGroups;

        // 属性实现
        public bool IsCollapsed => isCollapsed;
        public float Size => currentWidth;
        public float MinSize => minWidth;
        public float MaxSize => maxWidth;
        public System.Type TargetType => currentTarget?.GetType();

        public RightPanel()
        {
            elementName = "RightPanel";
            currentWidth = defaultWidth;
            propertyGroups = new Dictionary<string, Foldout>();
        }

        protected override void InitializeData()
        {
            // 设置初始状态
            SetupInitialState();

            // 初始化下拉菜单
            InitializeDropdowns();

            Logging.LogInfo("RightPanel", "右侧面板初始化完成");
        }

        protected override void CacheUIElements()
        {
            // 头部控制按钮
            collapseButton = SafeQuery<Button>("collapse-button");
            lockButton = SafeQuery<Button>("lock-button");
            settingsButton = SafeQuery<Button>("settings-button");

            // 对象信息
            objectActiveToggle = SafeQuery<Toggle>("object-active");
            objectNameField = SafeQuery<TextField>("object-name");
            objectIconButton = SafeQuery<Button>("object-icon");
            tagDropdown = SafeQuery<DropdownField>("tag-dropdown");
            layerDropdown = SafeQuery<DropdownField>("layer-dropdown");

            // 主要内容
            propertiesScroll = SafeQuery<ScrollView>("properties-scroll");
            propertiesContainer = SafeQuery<VisualElement>("properties-container");
            emptyState = SafeQuery<VisualElement>("empty-state");

            // 组件控制
            addComponentButton = SafeQuery<Button>("add-component");
            componentMenuButton = SafeQuery<Button>("component-menu");
            componentDropdown = SafeQuery<VisualElement>("component-dropdown");

            // 调整大小手柄
            resizeHandle = SafeQuery<VisualElement>("resize-handle");

            // 缓存属性字段
            CachePropertyFields();

            // 缓存属性组
            CachePropertyGroups();
        }

        private void CachePropertyFields()
        {
            // 变换属性
            positionField = SafeQuery<Vector3Field>("position-field");
            rotationField = SafeQuery<Vector3Field>("rotation-field");
            scaleField = SafeQuery<Vector3Field>("scale-field");

            // 渲染属性 - 直接查找自定义字段
            colorField = SafeQuery<CustomColorField>("color-field");
            alphaSlider = SafeQuery<Slider>("alpha-slider");
            materialField = SafeQuery<CustomObjectField>("material-field");
            castShadowsToggle = SafeQuery<Toggle>("cast-shadows");
            receiveShadowsToggle = SafeQuery<Toggle>("receive-shadows");

            // 物理属性
            massField = SafeQuery<FloatField>("mass-field");
            dragField = SafeQuery<FloatField>("drag-field");
            angularDragField = SafeQuery<FloatField>("angular-drag-field");
            useGravityToggle = SafeQuery<Toggle>("use-gravity");
            isKinematicToggle = SafeQuery<Toggle>("is-kinematic");

            // 碰撞属性
            colliderTypeDropdown = SafeQuery<DropdownField>("collider-type");
            isTriggerToggle = SafeQuery<Toggle>("is-trigger");
            colliderCenterField = SafeQuery<Vector3Field>("collider-center");
            colliderSizeField = SafeQuery<Vector3Field>("collider-size");
        }

        private void CachePropertyGroups()
        {
            propertyGroups["transform"] = SafeQuery<Foldout>("transform-group");
            propertyGroups["renderer"] = SafeQuery<Foldout>("renderer-group");
            propertyGroups["physics"] = SafeQuery<Foldout>("physics-group");
            propertyGroups["collider"] = SafeQuery<Foldout>("collider-group");
        }

        private void SetupInitialState()
        {
            // 设置面板宽度
            this.style.width = currentWidth;

            // 设置折叠状态
            if (isCollapsed)
            {
                SetCollapsed(true);
            }

            // 设置锁定状态
            if (isLocked)
            {
                SetLocked(true);
            }

            // 隐藏组件下拉菜单
            if (componentDropdown != null)
            {
                componentDropdown.style.display = DisplayStyle.None;
            }
        }

        private void InitializeDropdowns()
        {
            // 初始化标签下拉菜单
            if (tagDropdown != null)
            {
                tagDropdown.choices = new List<string> { "Untagged", "Player", "Enemy", "Environment", "UI" };
                tagDropdown.value = "Untagged";
            }

            // 初始化图层下拉菜单
            if (layerDropdown != null)
            {
                layerDropdown.choices = new List<string> { "Default", "UI", "Water", "Ground", "Player" };
                layerDropdown.value = "Default";
            }

            // 初始化碰撞器类型下拉菜单
            if (colliderTypeDropdown != null)
            {
                colliderTypeDropdown.choices = new List<string> { "Box", "Sphere", "Capsule", "Mesh" };
                colliderTypeDropdown.value = "Box";
            }
        }

        protected override void SetupEventListeners()
        {
            // 头部控制按钮事件
            collapseButton?.RegisterCallback<ClickEvent>(evt => ToggleCollapsed());
            lockButton?.RegisterCallback<ClickEvent>(evt => ToggleLocked());
            settingsButton?.RegisterCallback<ClickEvent>(evt => ShowPanelSettings());

            // 对象信息事件
            objectActiveToggle?.RegisterCallback<ChangeEvent<bool>>(OnObjectActiveChanged);
            objectNameField?.RegisterCallback<ChangeEvent<string>>(OnObjectNameChanged);
            objectIconButton?.RegisterCallback<ClickEvent>(evt => ShowObjectTypeMenu());
            tagDropdown?.RegisterCallback<ChangeEvent<string>>(OnTagChanged);
            layerDropdown?.RegisterCallback<ChangeEvent<string>>(OnLayerChanged);

            // 属性字段事件
            SetupPropertyFieldEvents();

            // 组件控制事件
            addComponentButton?.RegisterCallback<ClickEvent>(evt => ShowAddComponentMenu());
            componentMenuButton?.RegisterCallback<ClickEvent>(evt => ToggleComponentMenu());

            // 调整大小事件
            SetupResizeEvents();

            // 全局点击事件（关闭下拉菜单）
            this.RegisterCallback<ClickEvent>(evt => HideComponentMenu());

            // 监听选择变化事件（使用新事件系统）
            // 事件订阅将在需要时通过新事件系统处理
        }

        private void SetupPropertyFieldEvents()
        {
            // 变换属性事件
            positionField?.RegisterValueChangedCallback(evt => OnPropertyChanged("position", evt.newValue));
            rotationField?.RegisterValueChangedCallback(evt => OnPropertyChanged("rotation", evt.newValue));
            scaleField?.RegisterValueChangedCallback(evt => OnPropertyChanged("scale", evt.newValue));

            // 渲染属性事件
            if (colorField != null)
                colorField.OnValueChanged += color => OnPropertyChanged("color", color);
            alphaSlider?.RegisterValueChangedCallback(evt => OnPropertyChanged("alpha", evt.newValue));
            if (materialField != null)
                materialField.OnValueChanged += material => OnPropertyChanged("material", material);
            castShadowsToggle?.RegisterValueChangedCallback(evt => OnPropertyChanged("castShadows", evt.newValue));
            receiveShadowsToggle?.RegisterValueChangedCallback(evt => OnPropertyChanged("receiveShadows", evt.newValue));

            // 物理属性事件
            massField?.RegisterValueChangedCallback(evt => OnPropertyChanged("mass", evt.newValue));
            dragField?.RegisterValueChangedCallback(evt => OnPropertyChanged("drag", evt.newValue));
            angularDragField?.RegisterValueChangedCallback(evt => OnPropertyChanged("angularDrag", evt.newValue));
            useGravityToggle?.RegisterValueChangedCallback(evt => OnPropertyChanged("useGravity", evt.newValue));
            isKinematicToggle?.RegisterValueChangedCallback(evt => OnPropertyChanged("isKinematic", evt.newValue));

            // 碰撞属性事件
            colliderTypeDropdown?.RegisterValueChangedCallback(evt => OnPropertyChanged("colliderType", evt.newValue));
            isTriggerToggle?.RegisterValueChangedCallback(evt => OnPropertyChanged("isTrigger", evt.newValue));
            colliderCenterField?.RegisterValueChangedCallback(evt => OnPropertyChanged("colliderCenter", evt.newValue));
            colliderSizeField?.RegisterValueChangedCallback(evt => OnPropertyChanged("colliderSize", evt.newValue));
        }

        private void SetupResizeEvents()
        {
            if (resizeHandle != null)
            {
                resizeHandle.RegisterCallback<MouseDownEvent>(OnResizeStart);
                resizeHandle.RegisterCallback<MouseMoveEvent>(OnResizeMove);
                resizeHandle.RegisterCallback<MouseUpEvent>(OnResizeEnd);
            }
        }

        // 事件处理方法
        private void OnObjectActiveChanged(ChangeEvent<bool> evt)
        {
            OnPropertyChanged("active", evt.newValue);
            Logging.LogInfo("RightPanel", $"对象激活状态: {evt.newValue}");
        }

        private void OnObjectNameChanged(ChangeEvent<string> evt)
        {
            OnPropertyChanged("name", evt.newValue);
            Logging.LogInfo("RightPanel", $"对象名称: {evt.newValue}");
        }

        private void OnTagChanged(ChangeEvent<string> evt)
        {
            OnPropertyChanged("tag", evt.newValue);
            Logging.LogInfo("RightPanel", $"标签: {evt.newValue}");
        }

        private void OnLayerChanged(ChangeEvent<string> evt)
        {
            OnPropertyChanged("layer", evt.newValue);
            Logging.LogInfo("RightPanel", $"图层: {evt.newValue}");
        }

        private void OnPropertyChanged(string propertyName, object newValue)
        {
            if (isLocked) return;

            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new PropertyChangedEvent(null, propertyName, null, newValue, "RightPanel"));
            }
            Logging.LogInfo("RightPanel", $"属性变化: {propertyName} = {newValue}");
        }

        private void OnObjectSelected(object selectedObject)
        {
            if (isLocked) return;

            SetTarget(selectedObject);
            Logging.LogInfo("RightPanel", $"选择对象: {selectedObject}");
        }

        private void OnSelectionCleared()
        {
            if (isLocked) return;

            SetTarget(null);
            Logging.LogInfo("RightPanel", "清除选择");
        }

        private void ShowObjectTypeMenu()
        {
            Logging.LogInfo("RightPanel", "显示对象类型菜单");
        }

        private void ShowPanelSettings()
        {
            Logging.LogInfo("RightPanel", "显示面板设置");
        }

        private void ShowAddComponentMenu()
        {
            ToggleComponentMenu();
        }

        private void ToggleComponentMenu()
        {
            if (componentDropdown != null)
            {
                bool isVisible = componentDropdown.style.display == DisplayStyle.Flex;
                componentDropdown.style.display = isVisible ? DisplayStyle.None : DisplayStyle.Flex;
            }
        }

        private void HideComponentMenu()
        {
            if (componentDropdown != null)
            {
                componentDropdown.style.display = DisplayStyle.None;
            }
        }

        // ICollapsible 接口实现
        public void SetCollapsed(bool collapsed)
        {
            isCollapsed = collapsed;

            if (collapsed)
            {
                this.AddToClassList("collapsed");
            }
            else
            {
                this.RemoveFromClassList("collapsed");
            }

            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new CustomEvent($"panel-{(collapsed ? "collapsed" : "expanded")}", elementName, "RightPanel"));
            }
            Logging.LogInfo("RightPanel", $"面板折叠状态: {collapsed}");
        }

        public void ToggleCollapsed()
        {
            SetCollapsed(!isCollapsed);
        }

        // IResizable 接口实现
        public void SetSize(float size)
        {
            currentWidth = Mathf.Clamp(size, minWidth, maxWidth);
            if (!isCollapsed)
            {
                this.style.width = currentWidth;
            }

            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new CustomEvent("panel-resized", new { panelName = elementName, width = currentWidth }, "RightPanel"));
            }
        }

        public void ResetSize()
        {
            SetSize(defaultWidth);
        }

        // IPropertyEditor 接口实现
        public void SetTarget(object target)
        {
            currentTarget = target;

            if (target == null)
            {
                ShowEmptyState();
            }
            else
            {
                ShowPropertiesForTarget(target);
            }
        }

        public void RefreshProperties()
        {
            if (currentTarget != null)
            {
                ShowPropertiesForTarget(currentTarget);
            }
        }

        public void ApplyChanges()
        {
            Logging.LogInfo("RightPanel", "应用属性变化");
        }

        public void RevertChanges()
        {
            Logging.LogInfo("RightPanel", "撤销属性变化");
            RefreshProperties();
        }

        private void ShowEmptyState()
        {
            if (emptyState != null)
            {
                emptyState.style.display = DisplayStyle.Flex;
            }

            if (propertiesScroll != null)
            {
                propertiesScroll.style.display = DisplayStyle.None;
            }

            // 清空对象信息
            if (objectNameField != null)
            {
                objectNameField.value = "未选择对象";
            }
        }

        private void ShowPropertiesForTarget(object target)
        {
            if (emptyState != null)
            {
                emptyState.style.display = DisplayStyle.None;
            }

            if (propertiesScroll != null)
            {
                propertiesScroll.style.display = DisplayStyle.Flex;
            }

            // 更新对象信息
            UpdateObjectInfo(target);

            // 更新属性值
            UpdatePropertyValues(target);

            // 显示/隐藏相关属性组
            UpdatePropertyGroupVisibility(target);
        }

        private void UpdateObjectInfo(object target)
        {
            // 这里应该根据实际的目标对象类型来更新信息
            // 现在使用示例数据
            if (objectNameField != null)
            {
                objectNameField.value = target.ToString();
            }

            if (objectActiveToggle != null)
            {
                objectActiveToggle.value = true;
            }
        }

        private void UpdatePropertyValues(object target)
        {
            // 这里应该根据实际的目标对象来设置属性值
            // 现在使用示例数据
            string targetName = target?.ToString() ?? "Unknown";

            // 变换属性
            positionField?.SetValueWithoutNotify(Vector3.zero);
            rotationField?.SetValueWithoutNotify(Vector3.zero);
            scaleField?.SetValueWithoutNotify(Vector3.one);

            // 渲染属性
            colorField?.SetValueWithoutNotify(Color.white);
            alphaSlider?.SetValueWithoutNotify(1.0f);
            materialField?.SetValueWithoutNotify(null);
            castShadowsToggle?.SetValueWithoutNotify(true);
            receiveShadowsToggle?.SetValueWithoutNotify(true);

            // 物理属性
            massField?.SetValueWithoutNotify(1.0f);
            dragField?.SetValueWithoutNotify(0.0f);
            angularDragField?.SetValueWithoutNotify(0.05f);
            useGravityToggle?.SetValueWithoutNotify(true);
            isKinematicToggle?.SetValueWithoutNotify(false);

            // 碰撞属性
            isTriggerToggle?.SetValueWithoutNotify(false);
            colliderCenterField?.SetValueWithoutNotify(Vector3.zero);
            colliderSizeField?.SetValueWithoutNotify(Vector3.one);

            Logging.LogInfo("RightPanel", $"为目标 {targetName} 更新属性值");
        }

        private void UpdatePropertyGroupVisibility(object target)
        {
            // 根据目标对象类型显示/隐藏相关属性组
            // 这里使用示例逻辑，实际应该根据target的类型来决定
            string targetType = target?.GetType().Name ?? "Unknown";

            foreach (var group in propertyGroups.Values)
            {
                if (group != null)
                {
                    group.style.display = DisplayStyle.Flex;
                }
            }

            Logging.LogInfo("RightPanel", $"为目标类型 {targetType} 更新属性组可见性");
        }

        // 锁定功能
        public void SetLocked(bool locked)
        {
            isLocked = locked;

            if (locked)
            {
                this.AddToClassList("locked");
            }
            else
            {
                this.RemoveFromClassList("locked");
            }

            Logging.LogInfo("RightPanel", $"面板锁定状态: {locked}");
        }

        public void ToggleLocked()
        {
            SetLocked(!isLocked);
        }

        // 调整大小事件处理
        private void OnResizeStart(MouseDownEvent evt)
        {
            if (isCollapsed) return;

            isDragging = true;
            this.AddToClassList("resizing");
            resizeHandle?.CaptureMouse();
            evt.StopPropagation();
        }

        private void OnResizeMove(MouseMoveEvent evt)
        {
            if (!isDragging) return;

            var newWidth = currentWidth - evt.mouseDelta.x; // 注意：右侧面板是从左边拖拽
            SetSize(newWidth);
        }

        private void OnResizeEnd(MouseUpEvent evt)
        {
            if (!isDragging) return;

            isDragging = false;
            this.RemoveFromClassList("resizing");
            resizeHandle?.ReleaseMouse();
        }

        protected override void RemoveEventListeners()
        {
            // 旧事件系统监听器已移除
        }

        public override void Cleanup()
        {
            propertyGroups?.Clear();
            currentTarget = null;

            // 调用基类清理
            base.Cleanup();
        }
    }
}
