using UnityEngine;
using System;
using System.Collections;
using System.IO;
using System.Reflection;
using System.Linq;
using Crosstales.FB;
using BlastingDesign.UI.Core;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.Core
{
    /// <summary>
    /// 导入文件管理器 - 负责处理文件导入功能
    /// 当前支持：Cesium 3D Tileset索引文件(.json)导入
    /// 未来扩展：其他格式文件的导入
    /// </summary>
    public class ImportManager : MonoBehaviour
    {
        #region 单例模式
        public static ImportManager Instance { get; private set; }
        #endregion

        #region 配置参数
        [Header("导入设置")]
        [SerializeField] private bool enableImportFeature = true;
        [SerializeField] private bool debugMode = false;

        [Header("文件类型支持")]
        [SerializeField] private string[] supportedExtensions = { "json" };
        [SerializeField] private string defaultFileExtension = "json";

        [Header("Cesium设置")]
        [SerializeField] private bool autoFindCesiumTileset = true;
        [SerializeField] private GameObject cesiumTilesetObject; // 手动指定的Cesium 3D Tileset对象
        #endregion

        #region 私有变量
        private Component cesiumTilesetComponent;
        private FieldInfo urlField;
        private FieldInfo tilesetSourceField;
        private bool isInitialized = false;
        private IDisposable menuItemSubscription; // 新事件系统订阅
        #endregion

        #region Unity生命周期
        private void Awake()
        {
            // 单例模式初始化
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeImportManager();
            }
            else
            {
                Destroy(gameObject);
            }
        }

        private void OnEnable()
        {
            // 注册新事件系统监听
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                menuItemSubscription = eventSystemManager.EventBus.Subscribe<MenuItemClickedEvent>(OnMenuItemClicked);
                if (debugMode)
                {
                    Logging.LogInfo("ImportManager", "已注册新事件系统监听");
                }
            }
            else
            {
                // 延迟注册，等待EventSystemManager初始化
                StartCoroutine(DelayedEventRegistration());
            }
        }

        private void OnDisable()
        {
            // 注销新事件系统监听
            menuItemSubscription?.Dispose();
            if (debugMode)
            {
                Logging.LogInfo("ImportManager", "已注销新事件系统监听");
            }
        }
        #endregion

        #region 初始化方法
        /// <summary>
        /// 初始化导入管理器
        /// </summary>
        private void InitializeImportManager()
        {
            if (!enableImportFeature)
            {
                Logging.LogWarning("ImportManager", "导入功能已禁用");
                return;
            }

            // 查找Cesium 3D Tileset组件
            FindCesiumTilesetComponent();

            isInitialized = true;
            Logging.LogInfo("ImportManager", "导入管理器初始化完成");
        }

        /// <summary>
        /// 延迟注册事件监听
        /// </summary>
        private IEnumerator DelayedEventRegistration()
        {
            // 等待EventSystemManager初始化
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            while (eventSystemManager?.EventBus == null)
            {
                yield return new WaitForSeconds(0.1f);
                eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            }

            // 注册新事件系统监听
            menuItemSubscription = eventSystemManager.EventBus.Subscribe<MenuItemClickedEvent>(OnMenuItemClicked);
            if (debugMode)
            {
                Logging.LogInfo("ImportManager", "延迟注册新事件系统监听完成");
            }
        }
        #endregion

        #region Cesium组件查找
        /// <summary>
        /// 查找场景中的Cesium 3D Tileset组件
        /// </summary>
        private void FindCesiumTilesetComponent()
        {
            try
            {
                // 如果手动指定了对象，优先使用
                if (cesiumTilesetObject != null)
                {
                    cesiumTilesetComponent = FindCesiumComponentInGameObject(cesiumTilesetObject);
                    if (cesiumTilesetComponent != null)
                    {
                        CacheComponentFields();
                        Logging.LogInfo("ImportManager", $"使用手动指定的Cesium 3D Tileset: {cesiumTilesetObject.name}");
                        return;
                    }
                }

                // 自动查找Cesium 3D Tileset组件
                if (autoFindCesiumTileset)
                {
                    // 查找所有MonoBehaviour组件，寻找Cesium相关的组件
                    MonoBehaviour[] allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None);

                    foreach (MonoBehaviour component in allComponents)
                    {
                        if (IsCesiumTilesetComponent(component))
                        {
                            cesiumTilesetComponent = component;
                            cesiumTilesetObject = component.gameObject;
                            CacheComponentFields();
                            Logging.LogInfo("ImportManager", $"自动找到Cesium 3D Tileset: {component.gameObject.name}");
                            return;
                        }
                    }
                }

                Logging.LogWarning("ImportManager", "未找到Cesium 3D Tileset组件，请确保场景中存在该组件或手动指定");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"查找Cesium组件时发生错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 在指定GameObject中查找Cesium组件
        /// </summary>
        private Component FindCesiumComponentInGameObject(GameObject gameObject)
        {
            Component[] components = gameObject.GetComponents<Component>();
            foreach (Component component in components)
            {
                if (component != null && IsCesiumTilesetComponent(component))
                {
                    return component;
                }
            }
            return null;
        }

        /// <summary>
        /// 判断是否为Cesium 3D Tileset组件
        /// </summary>
        private bool IsCesiumTilesetComponent(Component component)
        {
            if (component == null) return false;

            string typeName = component.GetType().Name;
            string namespaceName = component.GetType().Namespace;

            // 检查是否为Cesium相关组件
            return (typeName.Contains("Cesium") && typeName.Contains("Tileset")) ||
                   (namespaceName != null && namespaceName.Contains("Cesium"));
        }

        /// <summary>
        /// 缓存组件字段信息
        /// </summary>
        private void CacheComponentFields()
        {
            if (cesiumTilesetComponent == null) return;

            try
            {
                System.Type componentType = cesiumTilesetComponent.GetType();

                // 查找URL字段（通常是_url或url）
                urlField = componentType.GetField("_url", BindingFlags.NonPublic | BindingFlags.Instance) ??
                          componentType.GetField("url", BindingFlags.Public | BindingFlags.Instance);

                // 查找TilesetSource字段（用于设置数据源类型）
                tilesetSourceField = componentType.GetField("_tilesetSource", BindingFlags.NonPublic | BindingFlags.Instance) ??
                                   componentType.GetField("tilesetSource", BindingFlags.Public | BindingFlags.Instance);

                if (urlField != null)
                {
                    Logging.LogInfo("ImportManager", "成功缓存Cesium组件字段信息");
                }
                else
                {
                    Logging.LogWarning("ImportManager", "未找到URL字段，可能无法更新Cesium URL");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"缓存组件字段时发生错误: {ex.Message}");
            }
        }
        #endregion

        #region 事件发布辅助方法

        /// <summary>
        /// 发布状态消息事件
        /// </summary>
        private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
        {
            var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "ImportManager"));
            }
            else
            {
                Logging.LogWarning("ImportManager", "EventSystemManager未初始化，无法发布状态消息");
            }
        }

        #endregion

        #region 事件处理
        /// <summary>
        /// 处理UI菜单项点击事件
        /// </summary>
        private void OnMenuItemClicked(MenuItemClickedEvent evt)
        {
            if (!enableImportFeature || !isInitialized)
            {
                return;
            }

            if (evt.MenuItem == "import")
            {
                if (debugMode)
                {
                    Logging.LogInfo("ImportManager", "收到导入菜单点击事件");
                }
                StartImportProcess();
            }
        }
        #endregion

        #region 导入处理
        /// <summary>
        /// 开始导入流程
        /// </summary>
        public void StartImportProcess()
        {
            if (!enableImportFeature)
            {
                Logging.LogWarning("ImportManager", "导入功能已禁用");
                return;
            }

            if (cesiumTilesetComponent == null)
            {
                Logging.LogWarning("ImportManager", "未找到Cesium 3D Tileset组件，尝试重新查找...");
                FindCesiumTilesetComponent();

                if (cesiumTilesetComponent == null)
                {
                    PublishStatusMessage("错误：未找到Cesium 3D Tileset组件", StatusMessageType.Error);
                    return;
                }
            }

            // 使用FileBrowser异步选择文件
            StartCoroutine(OpenFileDialog());
        }

        /// <summary>
        /// 打开文件选择对话框
        /// </summary>
        private IEnumerator OpenFileDialog()
        {
            PublishStatusMessage("正在打开文件选择对话框...");

            // 注册FileBrowser完成事件
            FileBrowser.Instance.OnOpenFilesComplete += OnFileSelected;

            try
            {
                // 异步打开文件选择对话框，限制为.json文件
                FileBrowser.Instance.OpenSingleFileAsync(defaultFileExtension);
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"打开文件对话框失败: {ex.Message}");
                PublishStatusMessage("错误：无法打开文件选择对话框", StatusMessageType.Error);
                FileBrowser.Instance.OnOpenFilesComplete -= OnFileSelected;
            }

            yield return null;
        }

        /// <summary>
        /// 处理文件选择结果
        /// </summary>
        private void OnFileSelected(bool selected, string singleFile, string[] files)
        {
            // 注销事件监听
            FileBrowser.Instance.OnOpenFilesComplete -= OnFileSelected;

            if (!selected || string.IsNullOrEmpty(singleFile))
            {
                PublishStatusMessage("取消文件选择", StatusMessageType.Warning);
                if (debugMode)
                {
                    Logging.LogInfo("ImportManager", "用户取消了文件选择");
                }
                return;
            }

            // 处理选中的文件
            ProcessSelectedFile(singleFile);
        }

        /// <summary>
        /// 处理选中的文件
        /// </summary>
        private void ProcessSelectedFile(string filePath)
        {
            try
            {
                if (debugMode)
                {
                    Logging.LogInfo("ImportManager", $"处理选中文件: {filePath}");
                }

                // 验证文件存在
                if (!File.Exists(filePath))
                {
                    Logging.LogError("ImportManager", $"文件不存在: {filePath}");
                    PublishStatusMessage("错误：选择的文件不存在", StatusMessageType.Error);
                    return;
                }

                // 验证文件扩展名
                string extension = Path.GetExtension(filePath).ToLower().TrimStart('.');
                if (!System.Array.Exists(supportedExtensions, ext => ext.ToLower() == extension))
                {
                    Logging.LogError("ImportManager", $"不支持的文件类型: {extension}");
                    PublishStatusMessage($"错误：不支持的文件类型 .{extension}", StatusMessageType.Error);
                    return;
                }

                // 转换为file:///格式的URL
                string fileUrl = ConvertToFileUrl(filePath);

                // 更新Cesium 3D Tileset的URL
                UpdateCesiumTilesetUrl(fileUrl);

                // 自动启用Cesium地形
                EnableCesiumGeoreference();

                // 提供成功反馈
                string fileName = Path.GetFileName(filePath);
                PublishStatusMessage($"成功导入: {fileName}", StatusMessageType.Success);
                Logging.LogInfo("ImportManager", $"成功导入文件: {fileName}");
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"处理文件时发生错误: {ex.Message}");
                PublishStatusMessage("错误：文件处理失败", StatusMessageType.Error);
            }
        }

        /// <summary>
        /// 将本地文件路径转换为file:///格式的URL
        /// </summary>
        private string ConvertToFileUrl(string filePath)
        {
            // 标准化路径分隔符
            string normalizedPath = filePath.Replace('\\', '/');

            // 如果已经是file://格式，直接返回
            if (normalizedPath.StartsWith("file://"))
            {
                return normalizedPath;
            }

            // 转换为file:///格式
            if (normalizedPath.StartsWith("/"))
            {
                return "file://" + normalizedPath;
            }
            else
            {
                return "file:///" + normalizedPath;
            }
        }

        /// <summary>
        /// 更新Cesium 3D Tileset的URL
        /// </summary>
        private void UpdateCesiumTilesetUrl(string newUrl)
        {
            if (cesiumTilesetComponent == null || urlField == null)
            {
                Logging.LogError("ImportManager", "无法更新Cesium URL：组件或字段引用无效");
                return;
            }

            try
            {
                // 首先设置TilesetSource为FromUrl，这是关键步骤
                if (tilesetSourceField != null)
                {
                    // 获取TilesetSource的枚举类型和值
                    System.Type tilesetSourceType = tilesetSourceField.FieldType;
                    object fromUrlValue = GetFromUrlEnumValue(tilesetSourceType);

                    if (fromUrlValue != null)
                    {
                        tilesetSourceField.SetValue(cesiumTilesetComponent, fromUrlValue);
                        Logging.LogInfo("ImportManager", $"已设置TilesetSource为FromUrl (值: {fromUrlValue})");
                    }
                    else
                    {
                        // 如果无法获取枚举值，尝试使用数值0（通常FromUrl是第一个枚举值）
                        tilesetSourceField.SetValue(cesiumTilesetComponent, 0);
                        Logging.LogWarning("ImportManager", "使用默认值0设置TilesetSource，请验证是否正确");
                    }
                }
                else
                {
                    Logging.LogWarning("ImportManager", "未找到TilesetSource字段，可能无法正确设置数据源类型");
                }

                // 更新URL字段
                urlField.SetValue(cesiumTilesetComponent, newUrl);

                Logging.LogInfo("ImportManager", $"已更新Cesium URL: {newUrl}");

                // 标记对象为已修改（用于编辑器）
#if UNITY_EDITOR
                UnityEditor.EditorUtility.SetDirty(cesiumTilesetComponent);
#endif

                // 提供成功反馈
                PublishStatusMessage($"成功设置Cesium URL为本地文件", StatusMessageType.Success);
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"更新Cesium URL时发生错误: {ex.Message}");
                PublishStatusMessage("错误：无法更新Cesium URL", StatusMessageType.Error);
            }
        }

        /// <summary>
        /// 获取FromUrl枚举值
        /// </summary>
        private object GetFromUrlEnumValue(System.Type enumType)
        {
            try
            {
                if (enumType.IsEnum)
                {
                    // 尝试获取名为"FromUrl"的枚举值
                    string[] enumNames = System.Enum.GetNames(enumType);
                    object[] enumValues = System.Enum.GetValues(enumType).Cast<object>().ToArray();

                    for (int i = 0; i < enumNames.Length; i++)
                    {
                        if (enumNames[i].Contains("FromUrl") || enumNames[i].Contains("Url"))
                        {
                            if (debugMode)
                            {
                                Logging.LogInfo("ImportManager", $"找到FromUrl枚举值: {enumNames[i]} = {enumValues[i]}");
                            }
                            return enumValues[i];
                        }
                    }

                    // 如果没找到包含"Url"的，尝试第一个值（通常是FromUrl）
                    if (enumValues.Length > 0)
                    {
                        if (debugMode)
                        {
                            Logging.LogInfo("ImportManager", $"使用第一个枚举值: {enumNames[0]} = {enumValues[0]}");
                        }
                        return enumValues[0];
                    }
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"获取FromUrl枚举值时发生错误: {ex.Message}");
            }

            return null;
        }
        #endregion

        #region 公共API
        /// <summary>
        /// 手动设置Cesium 3D Tileset对象
        /// </summary>
        public void SetCesiumTilesetObject(GameObject tilesetObject)
        {
            cesiumTilesetObject = tilesetObject;
            FindCesiumTilesetComponent();
        }

        /// <summary>
        /// 获取当前Cesium 3D Tileset的URL
        /// </summary>
        public string GetCurrentCesiumUrl()
        {
            if (cesiumTilesetComponent != null && urlField != null)
            {
                return urlField.GetValue(cesiumTilesetComponent) as string;
            }
            return null;
        }

        /// <summary>
        /// 获取当前TilesetSource的值
        /// </summary>
        public string GetCurrentTilesetSource()
        {
            if (cesiumTilesetComponent != null && tilesetSourceField != null)
            {
                object sourceValue = tilesetSourceField.GetValue(cesiumTilesetComponent);
                return sourceValue?.ToString() ?? "未知";
            }
            return "未设置";
        }

        /// <summary>
        /// 验证TilesetSource是否设置为FromUrl
        /// </summary>
        public bool IsTilesetSourceFromUrl()
        {
            if (cesiumTilesetComponent != null && tilesetSourceField != null)
            {
                try
                {
                    object currentValue = tilesetSourceField.GetValue(cesiumTilesetComponent);
                    System.Type enumType = tilesetSourceField.FieldType;
                    object fromUrlValue = GetFromUrlEnumValue(enumType);

                    return currentValue != null && fromUrlValue != null && currentValue.Equals(fromUrlValue);
                }
                catch (System.Exception ex)
                {
                    Logging.LogError("ImportManager", $"验证TilesetSource时发生错误: {ex.Message}");
                }
            }
            return false;
        }

        /// <summary>
        /// 检查导入功能是否可用
        /// </summary>
        public bool IsImportAvailable()
        {
            return enableImportFeature && isInitialized && cesiumTilesetComponent != null;
        }

        /// <summary>
        /// 自动启用Cesium地形
        /// </summary>
        private void EnableCesiumGeoreference()
        {
            try
            {
                // 通过InputManager启用Cesium地形
                InputManager inputManager = FindFirstObjectByType<InputManager>();
                if (inputManager != null)
                {
                    // 暂时禁用相机高度适应系统，防止导入时相机距离拉远
                    var cameraHeightSystem = inputManager.GetCameraHeightAdaptationSystem();
                    bool wasEnabled = false;

                    if (cameraHeightSystem != null)
                    {
                        wasEnabled = cameraHeightSystem.enableHeightAdaptation;
                        if (wasEnabled)
                        {
                            cameraHeightSystem.SuspendSystem(2.0f); // 暂停2秒，足够让Cesium地形稳定加载
                            Logging.LogInfo("ImportManager", "已暂时禁用相机高度适应系统");
                        }
                    }

                    // 启用Cesium地形
                    inputManager.EnableCesiumTerrain();
                    Logging.LogInfo("ImportManager", "已自动启用Cesium地形");

                    // 注意：SuspendSystem(2.0f)会自动在2秒后恢复系统，无需手动重新启用
                }
                else
                {
                    Logging.LogWarning("ImportManager", "InputManager实例不存在，无法自动启用Cesium地形");
                }
            }
            catch (System.Exception ex)
            {
                Logging.LogError("ImportManager", $"自动启用Cesium地形时发生错误: {ex.Message}");
            }
        }

        #endregion
    }
}
