using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.UIElements;
using BlastingDesign.Utils;

namespace BlastingDesign.Core.Input
{
    /// <summary>
    /// 输入事件优先级管理器
    /// 负责统一管理UI和场景交互事件的优先级，解决事件冲突问题
    /// </summary>
    public class InputEventPriorityManager : MonoBehaviour
    {
        #region 事件优先级枚举
        /// <summary>
        /// 事件优先级枚举（简化版）
        /// 二元判断：UI事件 vs 场景事件
        /// </summary>
        public enum EventPriority
        {
            UI_Event = 0,      // UI事件：鼠标在任何UI元素上
            Scene_Event = 1    // 场景事件：鼠标不在UI元素上，允许场景交互
        }

        /// <summary>
        /// 拖拽操作类型枚举
        /// </summary>
        public enum DragOperationType
        {
            None = 0,          // 无拖拽操作
            Rotation = 1,      // 旋转操作
            Panning = 2,       // 平移操作
            Zooming = 3,       // 缩放操作
            Selection = 4      // 选择操作
        }

        /// <summary>
        /// 事件源类型枚举
        /// </summary>
        public enum EventSourceType
        {
            Unknown = 0,       // 未知源
            Scene = 1,         // 源自三维场景
            UI = 2             // 源自UI界面
        }
        #endregion

        #region 字段定义
        [Header("优先级管理设置")]
        public bool enableDebugLogging = false;
        public bool showEventPriorityInGUI = false;

        [Header("拖拽持续性设置")]
        [SerializeField] private bool enableDragContinuity = true;
        [SerializeField] private float dragContinuityTimeout = 0.5f; // 拖拽操作超时时间（秒）

        [Header("事件源跟踪设置")]
        [SerializeField] private bool enableEventSourceTracking = true;
        [SerializeField] private bool enableEventSourceDebug = false;

        [Header("组件引用")]
        [SerializeField] private InputSystemManager inputSystemManager;

        // 缓存变量
        private EventPriority lastEventPriority = EventPriority.Scene_Event;
        private Vector2 lastMousePosition = Vector2.zero;
        private float lastCheckTime = 0f;
        private const float CHECK_INTERVAL = 0.016f; // 约60FPS的检查间隔

        // 拖拽持续性状态
        private bool isDragOperationActive = false;
        private float lastDragTime = 0f;
        private DragOperationType currentDragType = DragOperationType.None;

        // 事件源跟踪状态
        private EventSourceType currentEventSource = EventSourceType.Unknown;
        private Vector2 eventStartPosition = Vector2.zero;
        private bool eventSourceDetermined = false;

        // 单例模式
        public static InputEventPriorityManager Instance { get; private set; }
        #endregion

        #region 生命周期方法
        void Awake()
        {
            // 单例模式实现
            if (Instance == null)
            {
                Instance = this;
                DontDestroyOnLoad(gameObject);
                Logging.LogInfo("InputEventPriorityManager", "事件优先级管理器已初始化");
            }
            else
            {
                Logging.LogWarning("InputEventPriorityManager", "检测到重复的事件优先级管理器，销毁当前实例");
                Destroy(gameObject);
                return;
            }

            // 初始化组件引用
            InitializeComponents();
        }



        void OnGUI()
        {
            if (showEventPriorityInGUI)
            {
                Vector2 mousePos = Mouse.current.position.ReadValue();
                EventPriority priority = GetEventPriority(mousePos);

                GUI.Label(new Rect(10, 10, 300, 20), $"事件优先级: {GetPriorityDisplayName(priority)}");
                GUI.Label(new Rect(10, 30, 300, 20), $"鼠标位置: {mousePos}");

                int yOffset = 50;

                if (enableEventSourceTracking)
                {
                    GUI.Label(new Rect(10, yOffset, 300, 20), $"事件源跟踪: {(eventSourceDetermined ? "激活" : "未激活")}");
                    yOffset += 20;
                    if (eventSourceDetermined)
                    {
                        GUI.Label(new Rect(10, yOffset, 300, 20), $"事件源: {currentEventSource}");
                        yOffset += 20;
                        GUI.Label(new Rect(10, yOffset, 300, 20), $"事件起始位置: {eventStartPosition}");
                        yOffset += 20;
                    }
                }

                if (enableDragContinuity)
                {
                    GUI.Label(new Rect(10, yOffset, 300, 20), $"拖拽持续性: {(isDragOperationActive ? "激活" : "未激活")}");
                    yOffset += 20;
                    if (isDragOperationActive)
                    {
                        GUI.Label(new Rect(10, yOffset, 300, 20), $"拖拽类型: {currentDragType}");
                        yOffset += 20;
                        float remainingTime = dragContinuityTimeout - (Time.unscaledTime - lastDragTime);
                        GUI.Label(new Rect(10, yOffset, 300, 20), $"剩余时间: {Mathf.Max(0, remainingTime):F1}s");
                        yOffset += 20;
                    }
                }
            }
        }
        #endregion

        #region 初始化方法
        private void InitializeComponents()
        {
            // 查找EventSystemManager
            if (inputSystemManager == null)
            {
                inputSystemManager = FindFirstObjectByType<InputSystemManager>();
                if (inputSystemManager == null)
                {
                    Logging.LogWarning("InputEventPriorityManager", "未找到EventSystemManager，将使用Unity默认UI检测");
                }
            }
        }
        #endregion

        #region 核心API方法
        /// <summary>
        /// 获取指定鼠标位置的事件优先级（简化版）
        /// </summary>
        /// <param name="mousePosition">鼠标屏幕位置</param>
        /// <returns>事件优先级</returns>
        public EventPriority GetEventPriority(Vector2 mousePosition)
        {
            // 性能优化：避免过于频繁的检查
            float currentTime = Time.unscaledTime;
            if (currentTime - lastCheckTime < CHECK_INTERVAL &&
                Vector2.Distance(mousePosition, lastMousePosition) < 1f)
            {
                return lastEventPriority;
            }

            EventPriority priority = CalculateEventPriority();

            // 更新缓存
            lastEventPriority = priority;
            lastMousePosition = mousePosition;
            lastCheckTime = currentTime;

            if (enableDebugLogging)
            {
                Logging.LogInfo("InputEventPriorityManager",
                    $"鼠标位置 {mousePosition} 的事件优先级: {GetPriorityDisplayName(priority)}");
            }

            return priority;
        }

        /// <summary>
        /// 检查是否应该阻断场景事件（支持事件源跟踪和拖拽持续性）
        /// </summary>
        /// <param name="mousePosition">鼠标位置</param>
        /// <returns>true表示应该阻断场景事件</returns>
        public bool ShouldBlockSceneEvents(Vector2 mousePosition)
        {
            // 事件源优先级检查
            if (enableEventSourceTracking && ShouldRespectEventSource())
            {
                if (enableEventSourceDebug)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"事件源跟踪激活，不阻断场景事件。事件源: {currentEventSource}");
                }
                return false;
            }

            // 如果启用拖拽持续性且当前有拖拽操作，不阻断场景事件
            if (enableDragContinuity && IsDragOperationActive())
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"拖拽持续性激活，不阻断场景事件。当前拖拽类型: {currentDragType}");
                }
                return false;
            }

            EventPriority priority = GetEventPriority(mousePosition);
            return priority == EventPriority.UI_Event;
        }

        /// <summary>
        /// 检查是否允许场景交互（支持事件源跟踪和拖拽持续性）
        /// </summary>
        /// <param name="mousePosition">鼠标位置</param>
        /// <returns>true表示允许场景交互</returns>
        public bool AllowSceneInteraction(Vector2 mousePosition)
        {
            // 事件源优先级检查
            if (enableEventSourceTracking && ShouldRespectEventSource())
            {
                if (enableEventSourceDebug)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"事件源跟踪激活，允许场景交互。事件源: {currentEventSource}");
                }
                return true;
            }

            // 如果启用拖拽持续性且当前有拖拽操作，允许场景交互
            if (enableDragContinuity && IsDragOperationActive())
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"拖拽持续性激活，允许场景交互。当前拖拽类型: {currentDragType}");
                }
                return true;
            }

            EventPriority priority = GetEventPriority(mousePosition);
            return priority == EventPriority.Scene_Event;
        }
        #endregion

        #region 私有方法
        private EventPriority CalculateEventPriority()
        {
            // 简化版：只检查是否在UI元素上
            if (IsPointerOverUI())
            {
                return EventPriority.UI_Event;
            }

            // 不在UI上，允许场景交互
            return EventPriority.Scene_Event;
        }

        private bool IsPointerOverUI()
        {
            // 优先使用EventSystemManager的检测方法
            if (inputSystemManager != null)
            {
                return inputSystemManager.IsPointerOverUI();
            }

            // 备用方案：使用Unity的EventSystem
            return UnityEngine.EventSystems.EventSystem.current != null &&
                   UnityEngine.EventSystems.EventSystem.current.IsPointerOverGameObject();
        }

        private string GetPriorityDisplayName(EventPriority priority)
        {
            return priority switch
            {
                EventPriority.UI_Event => "UI事件",
                EventPriority.Scene_Event => "场景事件",
                _ => "未知"
            };
        }
        #endregion

        #region 事件源跟踪管理方法
        /// <summary>
        /// 开始事件源跟踪
        /// </summary>
        /// <param name="startPosition">事件开始位置</param>
        public void BeginEventSourceTracking(Vector2 startPosition)
        {
            if (!enableEventSourceTracking) return;

            eventStartPosition = startPosition;
            currentEventSource = DetermineEventSource(startPosition);
            eventSourceDetermined = true;

            if (enableEventSourceDebug)
            {
                Logging.LogInfo("InputEventPriorityManager",
                    $"开始事件源跟踪: 位置={startPosition}, 源={currentEventSource}");
            }
        }

        /// <summary>
        /// 结束事件源跟踪
        /// </summary>
        public void EndEventSourceTracking()
        {
            if (!enableEventSourceTracking) return;

            if (enableEventSourceDebug && eventSourceDetermined)
            {
                Logging.LogInfo("InputEventPriorityManager",
                    $"结束事件源跟踪: 源={currentEventSource}");
            }

            currentEventSource = EventSourceType.Unknown;
            eventStartPosition = Vector2.zero;
            eventSourceDetermined = false;
        }

        /// <summary>
        /// 确定事件源类型
        /// </summary>
        /// <param name="position">事件位置</param>
        /// <returns>事件源类型</returns>
        private EventSourceType DetermineEventSource(Vector2 position)
        {
            // 检查是否在UI元素上
            if (IsPointerOverUI())
            {
                return EventSourceType.UI;
            }
            else
            {
                return EventSourceType.Scene;
            }
        }

        /// <summary>
        /// 检查是否应该尊重事件源
        /// </summary>
        /// <returns>true表示事件源自场景，应该允许继续</returns>
        private bool ShouldRespectEventSource()
        {
            return eventSourceDetermined && currentEventSource == EventSourceType.Scene;
        }

        /// <summary>
        /// 获取当前事件源类型
        /// </summary>
        /// <returns>当前事件源类型</returns>
        public EventSourceType GetCurrentEventSource()
        {
            return currentEventSource;
        }

        /// <summary>
        /// 检查事件源是否已确定
        /// </summary>
        /// <returns>true表示事件源已确定</returns>
        public bool IsEventSourceDetermined()
        {
            return eventSourceDetermined;
        }
        #endregion

        #region 拖拽持续性管理方法
        /// <summary>
        /// 开始拖拽操作（支持事件源跟踪）
        /// </summary>
        /// <param name="dragType">拖拽类型</param>
        /// <param name="startPosition">拖拽开始位置</param>
        public void BeginDragOperation(DragOperationType dragType, Vector2 startPosition)
        {
            // 开始事件源跟踪
            if (enableEventSourceTracking)
            {
                BeginEventSourceTracking(startPosition);
            }

            // 开始拖拽操作
            if (enableDragContinuity)
            {
                isDragOperationActive = true;
                currentDragType = dragType;
                lastDragTime = Time.unscaledTime;

                if (enableDebugLogging)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"开始拖拽操作: {dragType}, 事件源: {currentEventSource}");
                }
            }
        }

        /// <summary>
        /// 开始拖拽操作（向后兼容方法）
        /// </summary>
        /// <param name="dragType">拖拽类型</param>
        public void BeginDragOperation(DragOperationType dragType)
        {
            Vector2 mousePosition = Mouse.current?.position.ReadValue() ?? Vector2.zero;
            BeginDragOperation(dragType, mousePosition);
        }

        /// <summary>
        /// 结束拖拽操作
        /// </summary>
        public void EndDragOperation()
        {
            if (enableDebugLogging && isDragOperationActive)
            {
                Logging.LogInfo("InputEventPriorityManager",
                    $"结束拖拽操作: {currentDragType}, 事件源: {currentEventSource}");
            }

            // 结束拖拽操作
            if (enableDragContinuity)
            {
                isDragOperationActive = false;
                currentDragType = DragOperationType.None;
                lastDragTime = 0f;
            }

            // 结束事件源跟踪
            if (enableEventSourceTracking)
            {
                EndEventSourceTracking();
            }
        }

        /// <summary>
        /// 更新拖拽操作状态
        /// </summary>
        public void UpdateDragOperation()
        {
            if (!enableDragContinuity || !isDragOperationActive) return;

            // 检查拖拽操作是否超时
            if (Time.unscaledTime - lastDragTime > dragContinuityTimeout)
            {
                if (enableDebugLogging)
                {
                    Logging.LogInfo("InputEventPriorityManager",
                        $"拖拽操作超时，自动结束: {currentDragType}");
                }
                EndDragOperation();
            }
        }

        /// <summary>
        /// 检查是否有拖拽操作正在进行
        /// </summary>
        /// <returns>true表示有拖拽操作正在进行</returns>
        public bool IsDragOperationActive()
        {
            if (!enableDragContinuity) return false;

            UpdateDragOperation(); // 自动检查超时
            return isDragOperationActive;
        }

        /// <summary>
        /// 获取当前拖拽操作类型
        /// </summary>
        /// <returns>当前拖拽操作类型</returns>
        public DragOperationType GetCurrentDragType()
        {
            return currentDragType;
        }
        #endregion

        #region 公共配置方法
        /// <summary>
        /// 设置调试日志开关
        /// </summary>
        public void SetDebugLogging(bool enabled)
        {
            enableDebugLogging = enabled;
        }

        /// <summary>
        /// 设置GUI显示开关
        /// </summary>
        public void SetGUIDisplay(bool enabled)
        {
            showEventPriorityInGUI = enabled;
        }

        /// <summary>
        /// 设置拖拽持续性开关
        /// </summary>
        public void SetDragContinuity(bool enabled)
        {
            enableDragContinuity = enabled;
            if (!enabled)
            {
                // 如果禁用拖拽持续性，清除当前拖拽状态
                EndDragOperation();
            }
        }

        /// <summary>
        /// 设置拖拽持续性超时时间
        /// </summary>
        public void SetDragContinuityTimeout(float timeout)
        {
            dragContinuityTimeout = Mathf.Max(0.1f, timeout);
        }
        #endregion
    }
}
