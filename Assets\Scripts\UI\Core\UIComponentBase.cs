using System;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Core
{
    /// <summary>
    /// UI组件基类 - 所有UI组件的基础类
    /// </summary>
    public abstract class UIComponentBase : MonoBehaviour
    {
        [Header("Component Settings")]
        [SerializeField] protected string componentName;
        [SerializeField] protected bool isVisible = true;
        [SerializeField] protected bool debugMode = false;

        [Header("UI Assets")]
        [SerializeField] protected VisualTreeAsset uiAsset;
        [SerializeField] protected StyleSheet styleSheet;

        // 组件状态
        protected VisualElement rootElement;
        protected VisualElement parentContainer;
        protected IEventBus eventBus; // 新事件系统引用
        protected bool isInitialized = false;

        // 属性
        public string ComponentName => componentName;
        public bool IsVisible => isVisible;
        public bool IsInitialized => isInitialized;
        public VisualElement RootElement => rootElement;

        protected virtual void Awake()
        {
            // 获取新事件系统引用
            var eventSystemManager = EventSystemManager.Instance;
            if (eventSystemManager != null)
            {
                eventBus = eventSystemManager.EventBus;
                if (eventBus != null)
                {
                    Logging.LogInfo(componentName, "新事件系统已连接");
                }
                else
                {
                    Logging.LogWarning(componentName, "EventBus未初始化，某些功能可能无法正常工作");
                }
            }
            else
            {
                Logging.LogWarning(componentName, "EventSystemManager未找到，某些功能可能无法正常工作");
            }

            // 设置组件名称（如果未设置）
            if (string.IsNullOrEmpty(componentName))
            {
                componentName = GetType().Name;
            }
        }

        /// <summary>
        /// 设置组件资源
        /// </summary>
        /// <param name="asset">UXML资源</param>
        /// <param name="style">样式表</param>
        public virtual void SetAssets(VisualTreeAsset asset, StyleSheet style)
        {
            uiAsset = asset;
            styleSheet = style;
        }

        /// <summary>
        /// 初始化组件
        /// </summary>
        /// <param name="parent">父容器</param>
        public virtual void Initialize(VisualElement parent)
        {
            Logging.LogInfo(componentName, $"开始初始化组件 {componentName}");

            if (isInitialized)
            {
                Logging.LogWarning(componentName, "组件已经初始化，跳过重复初始化");
                return;
            }

            // 确保事件系统引用是最新的
            EnsureEventSystemReference();

            parentContainer = parent;
            Logging.LogInfo(componentName, $"父容器设置: {(parentContainer != null ? "成功" : "失败 - 父容器为null")}");

            // 创建UI元素
            Logging.LogInfo(componentName, "开始创建UI元素");
            if (!CreateUIElements())
            {
                Logging.LogError(componentName, "UI元素创建失败");
                return;
            }
            Logging.LogInfo(componentName, "UI元素创建成功");

            // 应用样式
            Logging.LogInfo(componentName, "开始应用样式");
            ApplyStyles();
            Logging.LogInfo(componentName, "样式应用完成");

            // 设置事件监听器
            Logging.LogInfo(componentName, "开始设置事件监听器");
            SetupEventListeners();
            Logging.LogInfo(componentName, "事件监听器设置完成");

            // 执行自定义初始化
            Logging.LogInfo(componentName, "开始执行自定义初始化");
            OnInitialize();
            Logging.LogInfo(componentName, "自定义初始化完成");

            // 设置可见性
            Logging.LogInfo(componentName, $"设置可见性: {isVisible}");
            SetVisibility(isVisible);

            isInitialized = true;
            Logging.LogInfo(componentName, "组件初始化完成");
        }

        /// <summary>
        /// 创建UI元素
        /// </summary>
        protected virtual bool CreateUIElements()
        {
            Logging.LogInfo(componentName, $"检查UI Asset: {(uiAsset != null ? uiAsset.name : "null")}");

            if (uiAsset == null)
            {
                Logging.LogError(componentName, "UI Asset未设置");
                return false;
            }

            // 从UXML创建元素
            Logging.LogInfo(componentName, $"从UXML实例化UI元素: {uiAsset.name}");
            rootElement = uiAsset.Instantiate();
            if (rootElement == null)
            {
                Logging.LogError(componentName, "无法从UXML创建UI元素");
                return false;
            }

            Logging.LogInfo(componentName, $"UI元素创建成功，根元素类型: {rootElement.GetType().Name}");

            // 添加到父容器
            if (parentContainer != null)
            {
                Logging.LogInfo(componentName, $"将UI元素添加到父容器");
                parentContainer.Add(rootElement);
                Logging.LogInfo(componentName, $"父容器现在有 {parentContainer.childCount} 个子元素");
            }
            else
            {
                Logging.LogWarning(componentName, "父容器为null，UI元素未添加到容器");
            }

            return true;
        }

        /// <summary>
        /// 应用样式
        /// </summary>
        protected virtual void ApplyStyles()
        {
            if (styleSheet != null && rootElement != null)
            {
                rootElement.styleSheets.Add(styleSheet);
                Logging.LogInfo(componentName, "样式表已应用");
            }
        }

        /// <summary>
        /// 设置事件监听器
        /// </summary>
        protected virtual void SetupEventListeners()
        {
            // 子类重写此方法来设置特定的事件监听器
        }

        /// <summary>
        /// 自定义初始化逻辑
        /// </summary>
        protected virtual void OnInitialize()
        {
            // 子类重写此方法来执行特定的初始化逻辑
        }

        /// <summary>
        /// 设置组件可见性
        /// </summary>
        public virtual void SetVisibility(bool visible)
        {
            isVisible = visible;
            if (rootElement != null)
            {
                rootElement.style.display = visible ? DisplayStyle.Flex : DisplayStyle.None;
                Logging.LogInfo(componentName, $"可见性设置为: {visible}");
            }
        }

        /// <summary>
        /// 切换组件可见性
        /// </summary>
        public virtual void ToggleVisibility()
        {
            SetVisibility(!isVisible);
        }

        /// <summary>
        /// 刷新组件
        /// </summary>
        public virtual void Refresh()
        {
            if (!isInitialized)
            {
                Logging.LogWarning(componentName, "组件未初始化，无法刷新");
                return;
            }

            OnRefresh();
            Logging.LogInfo(componentName, "组件已刷新");
        }

        /// <summary>
        /// 自定义刷新逻辑
        /// </summary>
        protected virtual void OnRefresh()
        {
            // 子类重写此方法来执行特定的刷新逻辑
        }

        /// <summary>
        /// 清理组件
        /// </summary>
        public virtual void Cleanup()
        {
            // 移除事件监听器
            RemoveEventListeners();

            // 执行自定义清理
            OnCleanup();

            // 从父容器移除
            if (rootElement != null && parentContainer != null)
            {
                parentContainer.Remove(rootElement);
            }

            isInitialized = false;
            Logging.LogInfo(componentName, "组件已清理");
        }

        /// <summary>
        /// 移除事件监听器
        /// </summary>
        protected virtual void RemoveEventListeners()
        {
            // 子类重写此方法来移除特定的事件监听器
        }

        /// <summary>
        /// 自定义清理逻辑
        /// </summary>
        protected virtual void OnCleanup()
        {
            // 子类重写此方法来执行特定的清理逻辑
        }

        /// <summary>
        /// 查找UI元素的便捷方法
        /// </summary>
        protected T FindElement<T>(string name) where T : VisualElement
        {
            return rootElement?.Q<T>(name);
        }

        #region 事件系统辅助方法

        /// <summary>
        /// 确保事件系统引用是最新的
        /// </summary>
        protected virtual void EnsureEventSystemReference()
        {
            if (eventBus == null)
            {
                var eventSystemManager = EventSystemManager.Instance;
                if (eventSystemManager != null)
                {
                    eventBus = eventSystemManager.EventBus;
                    if (eventBus != null)
                    {
                        Logging.LogInfo(componentName, "EventBus引用已更新");
                    }
                }
            }
        }

        /// <summary>
        /// 发布事件的便捷方法
        /// </summary>
        protected virtual void PublishEvent<T>(T eventData) where T : IEvent
        {
            EnsureEventSystemReference();

            if (eventBus != null)
            {
                eventBus.Publish(eventData);
            }
            else
            {
                Logging.LogWarning(componentName, "EventBus未初始化，无法发布事件");
            }
        }

        /// <summary>
        /// 订阅事件的便捷方法
        /// </summary>
        protected virtual IDisposable SubscribeEvent<T>(System.Action<T> handler) where T : IEvent
        {
            EnsureEventSystemReference();

            if (eventBus != null)
            {
                return eventBus.Subscribe<T>(handler);
            }
            else
            {
                Logging.LogWarning(componentName, "EventBus未初始化，无法订阅事件");
                return null;
            }
        }

        #endregion

        protected virtual void OnDestroy()
        {
            Cleanup();
        }
    }
}
