using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.Utils;
using BlastingDesign.UI.Core;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

namespace BlastingDesign.UI.Components
{
    /// <summary>
    /// 模态窗口管理器 - 单例类
    /// 负责模态窗口的创建、销毁、层级管理和事件通知
    /// </summary>
    public class ModalWindowManager : MonoBehaviour
    {
        #region 单例模式

        private static ModalWindowManager instance;
        public static ModalWindowManager Instance
        {
            get
            {
                if (instance == null)
                {
                    // 查找现有实例
                    instance = FindFirstObjectByType<ModalWindowManager>();

                    if (instance == null)
                    {
                        // 创建新的实例
                        var go = new GameObject("ModalWindowManager");
                        instance = go.AddComponent<ModalWindowManager>();
                        DontDestroyOnLoad(go);
                        Logging.LogInfo("ModalWindowManager", "创建了新的ModalWindowManager实例");
                    }
                }
                return instance;
            }
        }

        #endregion

        #region 配置和状态

        [Header("Manager Settings")]
        private readonly int baseDisplayOrder = 0;
        private readonly int displayOrderIncrement = 1;
        private readonly bool enableEventNotifications = true;
        private readonly bool debugMode = false;

        // 窗口管理
        private readonly List<ModalWindow> activeWindows = new();
        private readonly Dictionary<string, ModalWindow> namedWindows = new();
        private VisualElement rootContainer;
        private int currentDisplayOrder;

        // 事件
        public event Action<ModalWindow> OnWindowCreated;
        public event Action<ModalWindow> OnWindowClosed;
        public event Action<ModalWindow> OnWindowFocused;

        #endregion

        #region Unity生命周期

        private void Awake()
        {
            // 确保单例唯一性
            if (instance != null && instance != this)
            {
                Destroy(gameObject);
                return;
            }

            instance = this;
            DontDestroyOnLoad(gameObject);

            currentDisplayOrder = baseDisplayOrder;

            Logging.LogInfo("ModalWindowManager", "模态窗口管理器初始化完成");
        }

        private void Start()
        {
            // 获取根容器引用
            InitializeRootContainer();
        }

        private void OnDestroy()
        {
            // 清理所有窗口
            CloseAllWindows();

            if (instance == this)
            {
                instance = null;
            }
        }

        #endregion

        #region 事件发布辅助方法

        /// <summary>
        /// 发布状态消息事件
        /// </summary>
        private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
        {
            var eventSystemManager = EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "ModalWindowManager"));
            }
            else
            {
                Logging.LogWarning("ModalWindowManager", "EventSystemManager未初始化，无法发布状态消息事件");
            }
        }

        /// <summary>
        /// 发布自定义事件
        /// </summary>
        private void PublishCustomEvent(string eventName, object data = null)
        {
            var eventSystemManager = EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                var customEvent = new CustomEvent(eventName, data, "ModalWindowManager");
                eventSystemManager.EventBus.Publish(customEvent);
            }
            else
            {
                Logging.LogWarning("ModalWindowManager", "EventSystemManager未初始化，无法发布自定义事件");
            }
        }

        #endregion

        #region 初始化

        /// <summary>
        /// 初始化根容器
        /// </summary>
        private void InitializeRootContainer()
        {
            // 查找UIDocument组件
            var uiDocument = FindFirstObjectByType<UIDocument>();
            if (uiDocument != null && uiDocument.rootVisualElement != null)
            {
                rootContainer = uiDocument.rootVisualElement;
                Logging.LogInfo("ModalWindowManager", "根容器获取成功");
            }
            else
            {
                Logging.LogWarning("ModalWindowManager", "未找到UIDocument，模态窗口功能可能不可用");
            }
        }

        /// <summary>
        /// 手动设置根容器（用于特殊情况）
        /// </summary>
        public void SetRootContainer(VisualElement container)
        {
            rootContainer = container;
            Logging.LogInfo("ModalWindowManager", "手动设置根容器");
        }

        #endregion

        #region 窗口创建

        /// <summary>
        /// 创建模态窗口
        /// </summary>
        public ModalWindow CreateWindow(string title = "Modal Window", Vector2? size = null, Vector2? position = null)
        {
            if (rootContainer == null)
            {
                Logging.LogError("ModalWindowManager", "根容器未设置，无法创建模态窗口");
                return null;
            }

            try
            {
                // 创建窗口实例
                var window = new ModalWindow();

                // 设置属性
                window.Title = title;
                if (size.HasValue)
                {
                    window.Size = size.Value;
                }
                if (position.HasValue)
                {
                    window.Position = position.Value;
                }

                // 设置显示顺序
                currentDisplayOrder += displayOrderIncrement;
                window.DisplayOrder = currentDisplayOrder;

                // 注册事件
                window.OnWindowClosed += OnWindowClosedInternal;
                window.OnWindowFocused += OnWindowFocusedInternal;

                // 初始化窗口
                window.Initialize();

                // 添加到根容器
                rootContainer.Add(window);

                // 添加到管理列表
                activeWindows.Add(window);

                // 触发事件
                if (enableEventNotifications)
                {
                    OnWindowCreated?.Invoke(window);
                    PublishCustomEvent("ModalWindowCreated", window);
                }

                if (debugMode)
                {
                    Logging.LogInfo("ModalWindowManager", $"创建窗口成功 - 标题: {title}, 显示顺序: {window.DisplayOrder}");
                }

                return window;
            }
            catch (Exception ex)
            {
                Logging.LogError("ModalWindowManager", $"创建窗口失败: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 创建命名窗口（可通过名称查找）
        /// </summary>
        public ModalWindow CreateNamedWindow(string name, string title = null, Vector2? size = null, Vector2? position = null)
        {
            if (string.IsNullOrEmpty(name))
            {
                Logging.LogError("ModalWindowManager", "窗口名称不能为空");
                return null;
            }

            // 检查是否已存在同名窗口
            if (namedWindows.ContainsKey(name))
            {
                Logging.LogWarning("ModalWindowManager", $"已存在名为 '{name}' 的窗口，将返回现有窗口");
                return namedWindows[name];
            }

            // 创建窗口
            var window = CreateWindow(title ?? name, size, position);
            if (window != null)
            {
                namedWindows[name] = window;

                if (debugMode)
                {
                    Logging.LogInfo("ModalWindowManager", $"创建命名窗口成功 - 名称: {name}");
                }
            }

            return window;
        }

        /// <summary>
        /// 创建设置窗口（预定义类型）
        /// </summary>
        public ModalWindow CreateSettingsWindow()
        {
            var window = CreateNamedWindow("Settings", "设置", new Vector2(800, 600));

            if (window != null)
            {
                // 使用新的设置窗口控制器
                CreateAdvancedSettingsContent(window);
            }

            return window;
        }

        /// <summary>
        /// 创建高级设置窗口的内容
        /// </summary>
        private void CreateAdvancedSettingsContent(ModalWindow window)
        {
            try
            {
                // 创建设置窗口控制器
                var settingsController = new GameObject("SettingsController").AddComponent<SettingsWindowController>();

                // 创建设置窗口内容
                var settingsContent = settingsController.CreateSettingsWindow(null);

                if (settingsContent != null)
                {
                    // 绑定设置窗口事件
                    settingsController.OnSettingsApplied += (settings) =>
                    {
                        // 处理设置应用逻辑
                        PublishStatusMessage("设置已应用");
                        PublishCustomEvent("SettingsApplied", settings);
                        window.CloseWindow();
                    };

                    settingsController.OnSettingsCancelled += () =>
                    {
                        PublishStatusMessage("已取消设置更改");
                        window.CloseWindow();
                    };

                    settingsController.OnSettingsReset += () =>
                    {
                        PublishStatusMessage("设置已重置");
                        PublishCustomEvent("SettingsReset", null);
                    };

                    // 当窗口关闭时清理控制器
                    window.OnWindowClosed += (_) =>
                    {
                        if (settingsController != null)
                        {
                            settingsController.Cleanup();
                            if (settingsController.gameObject != null)
                            {
                                Destroy(settingsController.gameObject);
                            }
                        }
                    };

                    // 设置窗口内容
                    window.SetContent(settingsContent);

                    Logging.LogInfo("ModalWindowManager", "高级设置窗口创建成功");
                }
                else
                {
                    Logging.LogError("ModalWindowManager", "创建设置窗口内容失败");
                    // 回退到简单设置窗口
                    CreateSettingsContent(window);
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("ModalWindowManager", $"创建高级设置窗口时发生错误: {ex.Message}");
                // 回退到简单设置窗口
                CreateSettingsContent(window);
            }
        }

        /// <summary>
        /// 创建简单设置窗口的内容（回退方案）
        /// </summary>
        private void CreateSettingsContent(ModalWindow window)
        {
            var content = new VisualElement
            {
                style =
                {
                    flexDirection = FlexDirection.Column,
                    paddingTop = 16,
                    paddingBottom = 16
                }
            };

            // 添加一些示例设置项
            var titleLabel = new Label("应用程序设置");
            titleLabel.style.fontSize = 16;
            titleLabel.style.unityFontStyleAndWeight = FontStyle.Bold;
            titleLabel.style.color = new Color(0.9f, 0.9f, 0.9f);
            titleLabel.style.marginBottom = 16;
            content.Add(titleLabel);

            // 添加一个设置组
            var generalGroup = new Foldout { text = "常规设置" };
            generalGroup.style.marginBottom = 12;

            var enableSoundToggle = new Toggle("启用声音效果");
            enableSoundToggle.style.marginBottom = 8;
            generalGroup.Add(enableSoundToggle);

            var enableNotificationsToggle = new Toggle("启用通知");
            enableNotificationsToggle.style.marginBottom = 8;
            generalGroup.Add(enableNotificationsToggle);

            content.Add(generalGroup);

            // 添加图形设置组
            var graphicsGroup = new Foldout { text = "图形设置" };
            graphicsGroup.style.marginBottom = 12;

            var qualitySlider = new Slider("图形质量", 0, 3) { value = 2 };
            qualitySlider.style.marginBottom = 8;
            graphicsGroup.Add(qualitySlider);

            var fullscreenToggle = new Toggle("全屏模式");
            fullscreenToggle.style.marginBottom = 8;
            graphicsGroup.Add(fullscreenToggle);

            content.Add(graphicsGroup);

            // 添加按钮区域
            var buttonContainer = new VisualElement();
            buttonContainer.style.flexDirection = FlexDirection.Row;
            buttonContainer.style.justifyContent = Justify.FlexEnd;
            buttonContainer.style.marginTop = 20;

            var cancelButton = new Button(() => window.CloseWindow()) { text = "取消" };
            cancelButton.style.marginRight = 8;
            cancelButton.style.minWidth = 80;
            buttonContainer.Add(cancelButton);

            var applyButton = new Button(() =>
            {
                // 这里可以添加应用设置的逻辑
                PublishStatusMessage("设置已应用");
                window.CloseWindow();
            })
            { text = "应用" };
            applyButton.style.minWidth = 80;
            applyButton.style.backgroundColor = new Color(0.2f, 0.6f, 0.9f);
            buttonContainer.Add(applyButton);

            content.Add(buttonContainer);

            window.SetContent(content);
        }

        #endregion

        #region 窗口管理

        /// <summary>
        /// 关闭窗口
        /// </summary>
        public void CloseWindow(ModalWindow window)
        {
            if (window == null) return;

            try
            {
                // 从管理列表中移除
                activeWindows.Remove(window);

                // 从命名窗口字典中移除
                var nameToRemove = "";
                foreach (var kvp in namedWindows)
                {
                    if (kvp.Value == window)
                    {
                        nameToRemove = kvp.Key;
                        break;
                    }
                }
                if (!string.IsNullOrEmpty(nameToRemove))
                {
                    namedWindows.Remove(nameToRemove);
                }

                // 从UI树中移除
                window.RemoveFromHierarchy();

                // 清理窗口
                window.Cleanup();

                // 触发事件
                if (enableEventNotifications)
                {
                    OnWindowClosed?.Invoke(window);
                    PublishCustomEvent("ModalWindowClosed", window);
                }

                if (debugMode)
                {
                    Logging.LogInfo("ModalWindowManager", $"关闭窗口成功 - 标题: {window.Title}");
                }
            }
            catch (Exception ex)
            {
                Logging.LogError("ModalWindowManager", $"关闭窗口失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 关闭指定名称的窗口
        /// </summary>
        public void CloseWindow(string name)
        {
            if (namedWindows.TryGetValue(name, out var window))
            {
                CloseWindow(window);
            }
        }

        /// <summary>
        /// 关闭所有窗口
        /// </summary>
        public void CloseAllWindows()
        {
            var windowsToClose = new List<ModalWindow>(activeWindows);
            foreach (var window in windowsToClose)
            {
                CloseWindow(window);
            }
        }

        /// <summary>
        /// 将窗口移到前台
        /// </summary>
        public void BringWindowToFront(ModalWindow window)
        {
            if (window == null || !activeWindows.Contains(window) || rootContainer == null) return;

            // 更新显示顺序
            currentDisplayOrder += displayOrderIncrement;
            window.DisplayOrder = currentDisplayOrder;

            // 在UI层级中将窗口移到最后（最前台）
            window.RemoveFromHierarchy();
            rootContainer.Add(window);

            // 添加焦点样式
            window.AddToClassList("focused");

            // 移除其他窗口的焦点样式
            foreach (var otherWindow in activeWindows)
            {
                if (otherWindow != window)
                {
                    otherWindow.RemoveFromClassList("focused");
                }
            }

            if (debugMode)
            {
                Logging.LogInfo("ModalWindowManager", $"窗口移到前台 - 标题: {window.Title}, 显示顺序: {window.DisplayOrder}");
            }
        }

        #endregion

        #region 窗口查找

        /// <summary>
        /// 根据名称查找窗口
        /// </summary>
        public ModalWindow FindWindow(string name)
        {
            namedWindows.TryGetValue(name, out var window);
            return window;
        }

        /// <summary>
        /// 根据标题查找窗口
        /// </summary>
        public ModalWindow FindWindowByTitle(string title)
        {
            return activeWindows.Find(w => w.Title == title);
        }

        /// <summary>
        /// 获取所有活动窗口
        /// </summary>
        public List<ModalWindow> GetActiveWindows()
        {
            return new List<ModalWindow>(activeWindows);
        }

        /// <summary>
        /// 检查是否有活动窗口
        /// </summary>
        public bool HasActiveWindows()
        {
            return activeWindows.Count > 0;
        }

        /// <summary>
        /// 获取活动窗口数量
        /// </summary>
        public int GetActiveWindowCount()
        {
            return activeWindows.Count;
        }

        #endregion

        #region 内部事件处理

        private void OnWindowClosedInternal(ModalWindow _)
        {
            // 这个方法由窗口自身调用，不需要手动关闭窗口
            // 只需要处理管理器级别的清理
        }

        private void OnWindowFocusedInternal(ModalWindow window)
        {
            BringWindowToFront(window);

            // 触发事件
            if (enableEventNotifications)
            {
                OnWindowFocused?.Invoke(window);
            }
        }

        #endregion

        #region 实用工具

        /// <summary>
        /// 显示消息对话框
        /// </summary>
        public ModalWindow ShowMessageDialog(string title, string message, Action onOk = null)
        {
            var window = CreateWindow(title, new Vector2(350, 150));

            if (window != null)
            {
                var content = new VisualElement();
                content.style.flexDirection = FlexDirection.Column;
                content.style.justifyContent = Justify.SpaceBetween;
                content.style.height = Length.Percent(100);

                // 消息文本
                var messageLabel = new Label(message);
                messageLabel.style.whiteSpace = WhiteSpace.Normal;
                messageLabel.style.color = new Color(0.9f, 0.9f, 0.9f);
                messageLabel.style.marginBottom = 20;
                content.Add(messageLabel);

                // 按钮区域
                var buttonContainer = new VisualElement();
                buttonContainer.style.flexDirection = FlexDirection.Row;
                buttonContainer.style.justifyContent = Justify.FlexEnd;

                var okButton = new Button(() =>
                {
                    onOk?.Invoke();
                    window.CloseWindow();
                })
                { text = "确定" };
                okButton.style.minWidth = 80;
                okButton.style.backgroundColor = new Color(0.2f, 0.6f, 0.9f);
                buttonContainer.Add(okButton);

                content.Add(buttonContainer);
                window.SetContent(content);
            }

            return window;
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        public ModalWindow ShowConfirmDialog(string title, string message, Action onConfirm = null, Action onCancel = null)
        {
            var window = CreateWindow(title, new Vector2(400, 160));

            if (window != null)
            {
                var content = new VisualElement();
                content.style.flexDirection = FlexDirection.Column;
                content.style.justifyContent = Justify.SpaceBetween;
                content.style.height = Length.Percent(100);

                // 消息文本
                var messageLabel = new Label(message);
                messageLabel.style.whiteSpace = WhiteSpace.Normal;
                messageLabel.style.color = new Color(0.9f, 0.9f, 0.9f);
                messageLabel.style.marginBottom = 20;
                content.Add(messageLabel);

                // 按钮区域
                var buttonContainer = new VisualElement();
                buttonContainer.style.flexDirection = FlexDirection.Row;
                buttonContainer.style.justifyContent = Justify.FlexEnd;

                var cancelButton = new Button(() =>
                {
                    onCancel?.Invoke();
                    window.CloseWindow();
                })
                { text = "取消" };
                cancelButton.style.marginRight = 8;
                cancelButton.style.minWidth = 80;
                buttonContainer.Add(cancelButton);

                var confirmButton = new Button(() =>
                {
                    onConfirm?.Invoke();
                    window.CloseWindow();
                })
                { text = "确认" };
                confirmButton.style.minWidth = 80;
                confirmButton.style.backgroundColor = new Color(0.8f, 0.2f, 0.2f);
                buttonContainer.Add(confirmButton);

                content.Add(buttonContainer);
                window.SetContent(content);
            }

            return window;
        }

        #endregion

        #region 调试和统计

        /// <summary>
        /// 输出调试信息
        /// </summary>
        [ContextMenu("Debug Info")]
        public void PrintDebugInfo()
        {
            Logging.LogInfo("ModalWindowManager", $"活动窗口数量: {activeWindows.Count}");
            Logging.LogInfo("ModalWindowManager", $"命名窗口数量: {namedWindows.Count}");
            Logging.LogInfo("ModalWindowManager", $"当前显示顺序: {currentDisplayOrder}");

            foreach (var window in activeWindows)
            {
                Logging.LogInfo("ModalWindowManager", $"窗口 - 标题: {window.Title}, 显示顺序: {window.DisplayOrder}");
            }
        }

        #endregion
    }
}