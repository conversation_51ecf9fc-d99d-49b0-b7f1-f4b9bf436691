using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;
using BlastingDesign.UI.Core;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;
using System.Collections.Generic;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 树组件预览提供者
    /// </summary>
    public class TreePreviewProvider : BaseComponentPreviewProvider
    {
        #region 基础信息

        public override string ComponentId => "tree-item";
        public override string ComponentName => "树组件 (Tree)";
        public override string ComponentDescription => "树组件用于展示层次结构数据，支持展开收起、搜索过滤、多选模式等功能";

        #endregion

        #region 事件发布辅助方法

        /// <summary>
        /// 发布状态消息事件
        /// </summary>
        private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
        {
            var eventSystemManager = EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "TreePreviewProvider"));
            }
            else
            {
                Logging.LogWarning("TreePreviewProvider", "EventSystemManager未初始化，无法发布状态消息事件");
            }
        }

        #endregion

        #region 预览创建

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本树组件组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本树组件");
            basicSection.Add(basicTitle);

            var basicTreeContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基本树组件
            var basicTree = DaisyBuilder.Tree("basic-tree");
            CreateSampleTreeData(basicTree, "基本树示例");
            basicTreeContainer.Add(basicTree);

            basicSection.Add(basicTreeContainer);
            container.Add(basicSection);

            // 搜索树组件组
            var searchSection = new VisualElement();
            searchSection.AddToClassList("preview-section");

            var searchTitle = CreatePreviewTitle("搜索树组件");
            searchSection.Add(searchTitle);

            var searchTreeContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 搜索树组件
            var searchTree = DaisyBuilder.SearchTree("search-tree", "搜索内容...");
            CreateSampleTreeData(searchTree, "搜索树示例");
            searchTreeContainer.Add(searchTree);

            searchSection.Add(searchTreeContainer);
            container.Add(searchSection);

            // 多选树组件组
            var multiSelectSection = new VisualElement();
            multiSelectSection.AddToClassList("preview-section");

            var multiSelectTitle = CreatePreviewTitle("多选树组件");
            multiSelectSection.Add(multiSelectTitle);

            var multiSelectTreeContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 多选树组件
            var multiSelectTree = DaisyBuilder.MultiSelectTree("multi-select-tree")
                .OnItemSelect(itemId => PublishStatusMessage($"选中项目: {itemId}"))
                .OnItemDeselect(itemId => PublishStatusMessage($"取消选中项目: {itemId}"));
            CreateSampleTreeData(multiSelectTree, "多选树示例");
            multiSelectTreeContainer.Add(multiSelectTree);

            multiSelectSection.Add(multiSelectTreeContainer);
            container.Add(multiSelectSection);

            // 样式变体组
            var styleSection = new VisualElement();
            styleSection.AddToClassList("preview-section");

            var styleTitle = CreatePreviewTitle("样式变体");
            styleSection.Add(styleTitle);

            var styleTreeContainer = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 紧凑型树
            var compactTree = DaisyBuilder.CompactTree("compact-tree")
                .OnItemClick(data => PublishStatusMessage($"点击节点: {data.Id}"));
            CreateSampleTreeData(compactTree, "紧凑型树");

            var compactLabel = new Label("紧凑型树");
            compactLabel.AddToClassList("text-sm");
            compactLabel.AddToClassList("font-semibold");
            compactLabel.style.marginBottom = 8;
            styleTreeContainer.Add(compactLabel);
            styleTreeContainer.Add(compactTree);

            // 深色主题树
            var darkTree = DaisyBuilder.DarkTree("dark-tree")
                .OnItemExpand(data => PublishStatusMessage($"展开节点: {data.Id}"))
                .OnItemCollapse(data => PublishStatusMessage($"收起节点: {data.Id}"));
            CreateSampleTreeData(darkTree, "深色主题树");

            var darkLabel = new Label("深色主题树");
            darkLabel.AddToClassList("text-sm");
            darkLabel.AddToClassList("font-semibold");
            darkLabel.style.marginBottom = 8;
            styleTreeContainer.Add(darkLabel);
            styleTreeContainer.Add(darkTree);

            styleSection.Add(styleTreeContainer);
            container.Add(styleSection);

            // 预定义树类型组
            var predefinedSection = new VisualElement();
            predefinedSection.AddToClassList("preview-section");

            var predefinedTitle = CreatePreviewTitle("预定义树类型");
            predefinedSection.Add(predefinedTitle);

            var predefinedTreeContainer = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 文件树
            var fileTree = DaisyBuilder.FileTree("file-tree")
                .OnAction((itemId, actionId) => PublishStatusMessage($"执行操作: {actionId} 在项目 {itemId}"));
            CreateFileTreeData(fileTree);

            var fileLabel = new Label("文件浏览器树");
            fileLabel.AddToClassList("text-sm");
            fileLabel.AddToClassList("font-semibold");
            fileLabel.style.marginBottom = 8;
            predefinedTreeContainer.Add(fileLabel);
            predefinedTreeContainer.Add(fileTree);

            // 项目树
            var projectTree = DaisyBuilder.ProjectTree("project-tree")
                .OnItemSelect(itemId => PublishStatusMessage($"选择项目项: {itemId}"));
            CreateProjectTreeData(projectTree);

            var projectLabel = new Label("项目结构树");
            projectLabel.AddToClassList("text-sm");
            projectLabel.AddToClassList("font-semibold");
            projectLabel.style.marginBottom = 8;
            predefinedTreeContainer.Add(projectLabel);
            predefinedTreeContainer.Add(projectTree);

            // 设置树
            var settingsTree = DaisyBuilder.SettingsTree("settings-tree")
                .OnItemClick(itemId => PublishStatusMessage($"打开设置: {itemId}"));
            CreateSettingsTreeData(settingsTree);

            var settingsLabel = new Label("设置菜单树");
            settingsLabel.AddToClassList("text-sm");
            settingsLabel.AddToClassList("font-semibold");
            settingsLabel.style.marginBottom = 8;
            predefinedTreeContainer.Add(settingsLabel);
            predefinedTreeContainer.Add(settingsTree);

            predefinedSection.Add(predefinedTreeContainer);
            container.Add(predefinedSection);

            return container;
        }

        #endregion

        #region 辅助方法

        /// <summary>
        /// 创建示例树数据
        /// </summary>
        /// <param name="tree">树组件</param>
        /// <param name="prefix">前缀</param>
        private void CreateSampleTreeData(DaisyTree tree, string prefix)
        {
            var treeData = new List<DaisyTreeData>();

            // 创建根节点1 - 工作区
            var rootNode1 = new DaisyTreeData("root1", $"{prefix} - 工作区", "🌳");

            // 创建项目管理分支
            var projectManagement = rootNode1.AddChild("project_mgmt", "项目管理", "📁");

            // 任务管理
            var taskManagement = projectManagement.AddChild("task_mgmt", "任务管理", "📋");
            var activeTasks = taskManagement.AddChild("active_tasks", "进行中任务", "🔄");
            var task1 = activeTasks.AddChild("task1", "UI设计", "📄");
            task1.AddAction("edit", "编辑", "✏️");
            task1.AddAction("assign", "分配", "👤");
            task1.AddAction("complete", "完成", "✅");
            task1.AddAction("delete", "删除", "🗑️");

            var task2 = activeTasks.AddChild("task2", "后端开发", "📄");
            task2.AddAction("edit", "编辑", "✏️");
            task2.AddAction("review", "审核", "👁️");
            task2.AddAction("test", "测试", "🧪");
            task2.AddAction("delete", "删除", "🗑️");

            var pendingTasks = taskManagement.AddChild("pending_tasks", "待办任务", "⏳");
            var task3 = pendingTasks.AddChild("task3", "数据库设计", "📄");
            task3.AddAction("start", "开始", "▶️");
            task3.AddAction("edit", "编辑", "✏️");
            task3.AddAction("priority", "优先级", "🔥");
            task3.AddAction("delete", "删除", "🗑️");

            var completedTasks = taskManagement.AddChild("completed_tasks", "已完成任务", "✅");
            var task4 = completedTasks.AddChild("task4", "需求分析", "📄");
            task4.AddAction("view", "查看", "👁️");
            task4.AddAction("archive", "归档", "📦");
            task4.AddAction("clone", "克隆", "📋");

            // 团队协作
            var teamCollaboration = projectManagement.AddChild("team_collab", "团队协作", "👥");
            var teamMembers = teamCollaboration.AddChild("members", "团队成员", "👤");
            var developer1 = teamMembers.AddChild("dev1", "开发者-张三", "💻");
            developer1.AddAction("profile", "资料", "👤");
            developer1.AddAction("assign", "分配任务", "📋");
            developer1.AddAction("message", "消息", "💬");

            var designer1 = teamMembers.AddChild("designer1", "设计师-李四", "🎨");
            designer1.AddAction("profile", "资料", "👤");
            designer1.AddAction("portfolio", "作品集", "🎨");
            designer1.AddAction("message", "消息", "💬");

            var communications = teamCollaboration.AddChild("communications", "沟通记录", "💬");
            var meetings = communications.AddChild("meetings", "会议记录", "📅");
            var meeting1 = meetings.AddChild("meeting1", "周例会-20231215", "📅");
            meeting1.AddAction("view", "查看", "👁️");
            meeting1.AddAction("edit", "编辑", "✏️");
            meeting1.AddAction("share", "分享", "📤");

            var discussions = communications.AddChild("discussions", "讨论记录", "💭");
            var discussion1 = discussions.AddChild("disc1", "技术方案讨论", "💭");
            discussion1.AddAction("view", "查看", "👁️");
            discussion1.AddAction("reply", "回复", "💬");
            discussion1.AddAction("resolve", "解决", "✅");

            // 创建根节点2 - 资源库
            var rootNode2 = new DaisyTreeData("root2", $"{prefix} - 资源库", "🌳");

            // 知识库
            var knowledgeBase = rootNode2.AddChild("knowledge_base", "知识库", "📚");
            var documentation = knowledgeBase.AddChild("documentation", "文档", "📝");
            var apiDocs = documentation.AddChild("api_docs", "API文档", "📋");
            var userGuide = apiDocs.AddChild("user_guide", "用户指南", "📖");
            userGuide.AddAction("read", "阅读", "👁️");
            userGuide.AddAction("edit", "编辑", "✏️");
            userGuide.AddAction("translate", "翻译", "🌐");
            userGuide.AddAction("export", "导出", "📤");

            var devGuide = apiDocs.AddChild("dev_guide", "开发指南", "📖");
            devGuide.AddAction("read", "阅读", "👁️");
            devGuide.AddAction("update", "更新", "🔄");
            devGuide.AddAction("version", "版本", "🏷️");
            devGuide.AddAction("delete", "删除", "🗑️");

            var tutorials = documentation.AddChild("tutorials", "教程", "🎓");
            var basicTutorial = tutorials.AddChild("basic", "基础教程", "📚");
            var lesson1 = basicTutorial.AddChild("lesson1", "第1课：入门", "📖");
            lesson1.AddAction("study", "学习", "📚");
            lesson1.AddAction("practice", "练习", "💪");
            lesson1.AddAction("quiz", "测验", "❓");

            var advancedTutorial = tutorials.AddChild("advanced", "高级教程", "📚");
            var lesson2 = advancedTutorial.AddChild("lesson2", "第2课：进阶", "📖");
            lesson2.AddAction("study", "学习", "📚");
            lesson2.AddAction("project", "项目", "🏗️");
            lesson2.AddAction("certificate", "证书", "🏆");

            // 工具集
            var toolset = knowledgeBase.AddChild("toolset", "工具集", "🛠️");
            var devTools = toolset.AddChild("dev_tools", "开发工具", "💻");
            var codeEditor = devTools.AddChild("code_editor", "代码编辑器", "📝");
            var vscode = codeEditor.AddChild("vscode", "VS Code", "💻");
            vscode.AddAction("install", "安装", "📥");
            vscode.AddAction("configure", "配置", "⚙️");
            vscode.AddAction("plugin", "插件", "🔌");
            vscode.AddAction("update", "更新", "🔄");

            var designTools = toolset.AddChild("design_tools", "设计工具", "🎨");
            var figma = designTools.AddChild("figma", "Figma", "🎨");
            figma.AddAction("open", "打开", "📂");
            figma.AddAction("collaborate", "协作", "👥");
            figma.AddAction("export", "导出", "📤");
            figma.AddAction("template", "模板", "📄");

            // 创建根节点3 - 监控中心
            var rootNode3 = new DaisyTreeData("root3", $"{prefix} - 监控中心", "🌳");

            // 系统监控
            var systemMonitoring = rootNode3.AddChild("system_monitoring", "系统监控", "📊");
            var performance = systemMonitoring.AddChild("performance", "性能监控", "⚡");
            var cpuMonitoring = performance.AddChild("cpu", "CPU监控", "🖥️");
            var cpuUsage = cpuMonitoring.AddChild("cpu_usage", "CPU使用率", "📈");
            cpuUsage.AddAction("view", "查看", "👁️");
            cpuUsage.AddAction("alert", "警报", "🚨");
            cpuUsage.AddAction("analyze", "分析", "📊");
            cpuUsage.AddAction("optimize", "优化", "⚡");

            var memoryMonitoring = performance.AddChild("memory", "内存监控", "🧠");
            var memoryUsage = memoryMonitoring.AddChild("memory_usage", "内存使用率", "📈");
            memoryUsage.AddAction("view", "查看", "👁️");
            memoryUsage.AddAction("cleanup", "清理", "🧹");
            memoryUsage.AddAction("report", "报告", "📋");

            var networkMonitoring = performance.AddChild("network", "网络监控", "🌐");
            var bandwidth = networkMonitoring.AddChild("bandwidth", "带宽监控", "📡");
            bandwidth.AddAction("monitor", "监控", "📊");
            bandwidth.AddAction("limit", "限制", "⚖️");
            bandwidth.AddAction("history", "历史", "📜");

            // 安全监控
            var securityMonitoring = systemMonitoring.AddChild("security", "安全监控", "🔒");
            var accessControl = securityMonitoring.AddChild("access_control", "访问控制", "🚪");
            var loginAttempts = accessControl.AddChild("login_attempts", "登录尝试", "🔑");
            loginAttempts.AddAction("view", "查看", "👁️");
            loginAttempts.AddAction("block", "阻止", "🚫");
            loginAttempts.AddAction("whitelist", "白名单", "✅");
            loginAttempts.AddAction("report", "报告", "📋");

            // 添加到数据列表
            treeData.AddRange(new[] { rootNode1, rootNode2, rootNode3 });

            // 设置数据到树组件
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建文件树数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateFileTreeData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            // 项目根目录
            var projectFolder = new DaisyTreeData("project", "Unity项目", "📁");

            // 源代码文件夹
            var srcFolder = projectFolder.AddChild("src", "源代码", "📂");

            // 核心系统
            var coreSystem = srcFolder.AddChild("core", "核心系统", "📂");
            var gameManager = coreSystem.AddChild("managers", "管理器", "📂");
            var sceneManager = gameManager.AddChild("scene_mgr", "场景管理器", "📂");
            var sceneController = sceneManager.AddChild("scene_controller.cs", "场景控制器", "📄");
            sceneController.AddAction("open", "打开", "📂");
            sceneController.AddAction("edit", "编辑", "✏️");
            sceneController.AddAction("debug", "调试", "🐛");
            sceneController.AddAction("delete", "删除", "🗑️");

            var sceneTransition = sceneManager.AddChild("scene_transition.cs", "场景转换", "📄");
            sceneTransition.AddAction("open", "打开", "📂");
            sceneTransition.AddAction("edit", "编辑", "✏️");
            sceneTransition.AddAction("test", "测试", "🧪");
            sceneTransition.AddAction("delete", "删除", "🗑️");

            var gameplayManager = gameManager.AddChild("gameplay_mgr", "游戏管理器", "📂");
            var playerManager = gameplayManager.AddChild("player_manager.cs", "玩家管理器", "📄");
            playerManager.AddAction("open", "打开", "📂");
            playerManager.AddAction("edit", "编辑", "✏️");
            playerManager.AddAction("profile", "性能分析", "⚡");
            playerManager.AddAction("delete", "删除", "🗑️");

            var levelManager = gameplayManager.AddChild("level_manager.cs", "关卡管理器", "📄");
            levelManager.AddAction("open", "打开", "📂");
            levelManager.AddAction("edit", "编辑", "✏️");
            levelManager.AddAction("validate", "验证", "✅");
            levelManager.AddAction("delete", "删除", "🗑️");

            // 游戏逻辑
            var gameLogic = srcFolder.AddChild("gameplay", "游戏逻辑", "📂");
            var playerSystem = gameLogic.AddChild("player", "玩家系统", "📂");
            var playerController = playerSystem.AddChild("player_controller.cs", "玩家控制器", "📄");
            playerController.AddAction("open", "打开", "📂");
            playerController.AddAction("edit", "编辑", "✏️");
            playerController.AddAction("test", "测试", "🧪");
            playerController.AddAction("delete", "删除", "🗑️");

            var playerStats = playerSystem.AddChild("player_stats.cs", "玩家属性", "📄");
            playerStats.AddAction("open", "打开", "📂");
            playerStats.AddAction("edit", "编辑", "✏️");
            playerStats.AddAction("balance", "平衡性", "⚖️");
            playerStats.AddAction("delete", "删除", "🗑️");

            var enemySystem = gameLogic.AddChild("enemy", "敌人系统", "📂");
            var enemyAI = enemySystem.AddChild("ai", "AI系统", "📂");
            var behaviorTree = enemyAI.AddChild("behavior_tree.cs", "行为树", "📄");
            behaviorTree.AddAction("open", "打开", "📂");
            behaviorTree.AddAction("edit", "编辑", "✏️");
            behaviorTree.AddAction("debug", "调试", "🐛");
            behaviorTree.AddAction("optimize", "优化", "⚡");
            behaviorTree.AddAction("delete", "删除", "🗑️");

            var pathfinding = enemyAI.AddChild("pathfinding.cs", "寻路系统", "📄");
            pathfinding.AddAction("open", "打开", "📂");
            pathfinding.AddAction("edit", "编辑", "✏️");
            pathfinding.AddAction("visualize", "可视化", "👁️");
            pathfinding.AddAction("delete", "删除", "🗑️");

            // 资源文件夹
            var assetsFolder = projectFolder.AddChild("assets", "资源文件", "📂");

            // 贴图资源
            var texturesFolder = assetsFolder.AddChild("textures", "贴图", "📂");
            var characterTextures = texturesFolder.AddChild("characters", "角色贴图", "📂");
            var heroTextures = characterTextures.AddChild("hero", "英雄贴图", "📂");
            var heroIdle = heroTextures.AddChild("hero_idle.png", "英雄待机", "🖼️");
            heroIdle.AddAction("view", "查看", "👁️");
            heroIdle.AddAction("edit", "编辑", "✏️");
            heroIdle.AddAction("compress", "压缩", "📦");
            heroIdle.AddAction("delete", "删除", "🗑️");

            var heroRun = heroTextures.AddChild("hero_run.png", "英雄奔跑", "🖼️");
            heroRun.AddAction("view", "查看", "👁️");
            heroRun.AddAction("edit", "编辑", "✏️");
            heroRun.AddAction("animate", "动画", "🎬");
            heroRun.AddAction("delete", "删除", "🗑️");

            var environmentTextures = texturesFolder.AddChild("environment", "环境贴图", "📂");
            var backgroundTextures = environmentTextures.AddChild("backgrounds", "背景贴图", "📂");
            var forestBg = backgroundTextures.AddChild("forest.png", "森林背景", "🖼️");
            forestBg.AddAction("view", "查看", "👁️");
            forestBg.AddAction("edit", "编辑", "✏️");
            forestBg.AddAction("parallax", "视差", "📏");
            forestBg.AddAction("delete", "删除", "🗑️");

            // 音效资源
            var audioFolder = assetsFolder.AddChild("audio", "音效", "📂");
            var musicFolder = audioFolder.AddChild("music", "音乐", "📂");
            var bgmFolder = musicFolder.AddChild("bgm", "背景音乐", "📂");
            var menuBgm = bgmFolder.AddChild("menu_bgm.ogg", "菜单音乐", "🎵");
            menuBgm.AddAction("play", "播放", "▶️");
            menuBgm.AddAction("edit", "编辑", "✏️");
            menuBgm.AddAction("loop", "循环", "🔄");
            menuBgm.AddAction("delete", "删除", "🗑️");

            var gameBgm = bgmFolder.AddChild("game_bgm.ogg", "游戏音乐", "🎵");
            gameBgm.AddAction("play", "播放", "▶️");
            gameBgm.AddAction("edit", "编辑", "✏️");
            gameBgm.AddAction("volume", "音量", "🔊");
            gameBgm.AddAction("delete", "删除", "🗑️");

            var sfxFolder = audioFolder.AddChild("sfx", "音效", "📂");
            var uiSfx = sfxFolder.AddChild("ui", "UI音效", "📂");
            var clickSfx = uiSfx.AddChild("click.wav", "点击音效", "🔊");
            clickSfx.AddAction("play", "播放", "▶️");
            clickSfx.AddAction("edit", "编辑", "✏️");
            clickSfx.AddAction("normalize", "标准化", "📊");
            clickSfx.AddAction("delete", "删除", "🗑️");

            // 配置文件夹
            var configFolder = projectFolder.AddChild("config", "配置", "📂");
            var gameConfig = configFolder.AddChild("game", "游戏配置", "📂");
            var levelConfig = gameConfig.AddChild("levels", "关卡配置", "📂");
            var level1Config = levelConfig.AddChild("level1.json", "第一关配置", "⚙️");
            level1Config.AddAction("open", "打开", "📂");
            level1Config.AddAction("edit", "编辑", "✏️");
            level1Config.AddAction("validate", "验证", "✅");
            level1Config.AddAction("backup", "备份", "💾");
            level1Config.AddAction("delete", "删除", "🗑️");

            var difficultyConfig = gameConfig.AddChild("difficulty.json", "难度配置", "⚙️");
            difficultyConfig.AddAction("open", "打开", "📂");
            difficultyConfig.AddAction("edit", "编辑", "✏️");
            difficultyConfig.AddAction("balance", "平衡", "⚖️");
            difficultyConfig.AddAction("delete", "删除", "🗑️");

            var systemConfig = configFolder.AddChild("system", "系统配置", "📂");
            var inputConfig = systemConfig.AddChild("input.json", "输入配置", "⚙️");
            inputConfig.AddAction("open", "打开", "📂");
            inputConfig.AddAction("edit", "编辑", "✏️");
            inputConfig.AddAction("test", "测试", "🧪");
            inputConfig.AddAction("reset", "重置", "🔄");
            inputConfig.AddAction("delete", "删除", "🗑️");

            // 文档文件夹
            var docsFolder = projectFolder.AddChild("docs", "文档", "📚");
            var designDocs = docsFolder.AddChild("design", "设计文档", "📂");
            var gameDesign = designDocs.AddChild("game_design.md", "游戏设计", "📝");
            gameDesign.AddAction("open", "打开", "📂");
            gameDesign.AddAction("edit", "编辑", "✏️");
            gameDesign.AddAction("export", "导出", "📤");
            gameDesign.AddAction("version", "版本", "🏷️");
            gameDesign.AddAction("delete", "删除", "🗑️");

            var levelDesign = designDocs.AddChild("level_design.md", "关卡设计", "📝");
            levelDesign.AddAction("open", "打开", "📂");
            levelDesign.AddAction("edit", "编辑", "✏️");
            levelDesign.AddAction("diagram", "图表", "📊");
            levelDesign.AddAction("delete", "删除", "🗑️");

            var techDocs = docsFolder.AddChild("technical", "技术文档", "📂");
            var architecture = techDocs.AddChild("architecture.md", "架构文档", "📝");
            architecture.AddAction("open", "打开", "📂");
            architecture.AddAction("edit", "编辑", "✏️");
            architecture.AddAction("review", "审核", "👁️");
            architecture.AddAction("update", "更新", "🔄");
            architecture.AddAction("delete", "删除", "🗑️");

            var apiDocs = techDocs.AddChild("api.md", "API文档", "📝");
            apiDocs.AddAction("open", "打开", "📂");
            apiDocs.AddAction("edit", "编辑", "✏️");
            apiDocs.AddAction("generate", "生成", "🔄");
            apiDocs.AddAction("publish", "发布", "📤");
            apiDocs.AddAction("delete", "删除", "🗑️");

            // 添加到数据列表
            treeData.Add(projectFolder);

            // 设置数据到树组件
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建项目树数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateProjectTreeData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var scriptsFolder = new DaisyTreeData("scripts", "脚本", "📁");
            var playerFile = scriptsFolder.AddChild("player.cs", "玩家控制器", "🎮");
            playerFile.AddAction("open", "打开", "📂");
            playerFile.AddAction("debug", "调试", "🐛");

            var enemyFile = scriptsFolder.AddChild("enemy.cs", "敌人AI", "🤖");
            enemyFile.AddAction("open", "打开", "📂");
            enemyFile.AddAction("debug", "调试", "🐛");

            var uiFolder = new DaisyTreeData("ui", "用户界面", "📁");
            var mainmenuFile = uiFolder.AddChild("mainmenu.uxml", "主菜单", "🎨");
            mainmenuFile.AddAction("open", "打开", "📂");
            mainmenuFile.AddAction("preview", "预览", "👁️");

            var hudFile = uiFolder.AddChild("hud.uxml", "游戏界面", "📊");
            hudFile.AddAction("open", "打开", "📂");
            hudFile.AddAction("preview", "预览", "👁️");

            var scenesFolder = new DaisyTreeData("scenes", "场景", "📁");
            var level1File = scenesFolder.AddChild("level1.unity", "第一关", "🎮");
            level1File.AddAction("open", "打开", "📂");
            level1File.AddAction("build", "构建", "🔧");

            var level2File = scenesFolder.AddChild("level2.unity", "第二关", "🎮");
            level2File.AddAction("open", "打开", "📂");
            level2File.AddAction("build", "构建", "🔧");

            treeData.AddRange(new[] { scriptsFolder, uiFolder, scenesFolder });
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建设置树数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateSettingsTreeData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var generalNode = new DaisyTreeData("general", "常规设置", "⚙️");
            generalNode.AddChild("theme", "主题设置", "🎨");
            generalNode.AddChild("language", "语言设置", "🌐");
            generalNode.AddChild("timezone", "时区设置", "🕐");

            var displayNode = new DaisyTreeData("display", "显示设置", "🖥️");
            displayNode.AddChild("resolution", "分辨率", "📺");
            displayNode.AddChild("brightness", "亮度", "💡");
            displayNode.AddChild("color", "颜色配置", "🎨");

            var securityNode = new DaisyTreeData("security", "安全设置", "🔒");
            securityNode.AddChild("password", "密码设置", "🔑");
            securityNode.AddChild("2fa", "双重验证", "🛡️");
            securityNode.AddChild("backup", "备份设置", "💾");

            var notificationNode = new DaisyTreeData("notifications", "通知设置", "🔔");
            notificationNode.AddChild("email", "邮件通知", "📧");
            notificationNode.AddChild("push", "推送通知", "📱");
            notificationNode.AddChild("sound", "声音设置", "🔊");

            treeData.AddRange(new[] { generalNode, displayNode, securityNode, notificationNode });
            tree.SetData(treeData);
        }

        #endregion

        #region 代码示例

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.DaisyUI.Components.Navigation.Tree;

// 创建基本树组件
var tree = DaisyBuilder.Tree(""my-tree"")
    .OnNodeClick(nodeId => Debug.Log($""点击节点: {nodeId}""))
    .OnItemSelect(itemId => Debug.Log($""选择项目: {itemId}""));

// 添加节点和项目
var rootNode = tree.AddNode(""root1"", ""根节点"", ""🌳"");
var childNode = rootNode.AddChildNode(""child1"", ""子节点"", ""📁"");
childNode.AddChildItem(""item1"", ""项目1"", ""📄"")
    .AddAction(""edit"", ""编辑"", ""✏️"")
    .AddAction(""delete"", ""删除"", ""🗑️"");

// 使用预定义树类型
var fileTree = DaisyBuilder.FileTree(""file-browser"")
    .OnAction((itemId, actionId) => {
        Debug.Log($""执行操作: {actionId} 在项目 {itemId}"");
    });

var searchTree = DaisyBuilder.SearchTree(""search-tree"", ""搜索文件..."")
    .OnSearch(query => Debug.Log($""搜索: {query}""));

var multiSelectTree = DaisyBuilder.MultiSelectTree(""multi-tree"")
    .OnItemSelect(itemId => Debug.Log($""选中: {itemId}""))
    .OnItemDeselect(itemId => Debug.Log($""取消选中: {itemId}""));

// 数据绑定方式
var rootData = new DaisyTreeData(""root"", ""根节点"", ""🌳"");
var childData = rootData.AddChild(""child"", ""子节点"", ""📁"");
childData.AddChild(""item"", ""项目"", ""📄"");

var dataTree = DaisyBuilder.Tree(rootData)
    .SetAllowSearch(true)
    .SetMultiSelect(true);

// 样式配置
tree.SetDarkTheme(true)     // 深色主题
    .SetCompact(true)       // 紧凑布局
    .SetBordered(true)      // 边框样式
    .SetShowLines(true)     // 显示连接线
    .SetShowIcons(true)     // 显示图标
    .SetShowActions(true)   // 显示操作按钮
    .SetAllowSearch(true)   // 允许搜索
    .SetSearchPlaceholder(""搜索..."");

// 程序化操作
tree.ExpandNode(""node1"");         // 展开节点
tree.CollapseNode(""node1"");       // 收起节点
tree.SelectItem(""item1"");         // 选择项目
tree.DeselectItem(""item1"");       // 取消选择
tree.ExpandAll();                   // 展开所有
tree.CollapseAll();                 // 收起所有

// 添加到容器
container.Add(tree);";
        }

        #endregion

        #region 使用说明

        public override string GetUsageInstructions()
        {
            return @"树组件使用说明：

1. 基本用法：
   - 使用 DaisyBuilder.Tree() 创建基本树组件
   - 使用 AddNode() 添加节点，AddItem() 添加叶子项
   - 节点可以包含子节点和项目

2. 预定义树类型：
   - FileTree()：文件浏览器风格，带搜索和操作按钮
   - ProjectTree()：项目结构风格，深色主题
   - SettingsTree()：设置菜单风格，隐藏连接线
   - SearchTree()：带搜索功能的树
   - MultiSelectTree()：支持多选的树
   - DarkTree()：深色主题树
   - CompactTree()：紧凑布局树

3. 数据绑定：
   - 使用 DaisyTreeData 创建数据结构
   - 支持层次化数据绑定
   - 自动同步数据状态

4. 样式配置：
   - SetDarkTheme(bool)：设置深色主题
   - SetCompact(bool)：设置紧凑布局
   - SetBordered(bool)：设置边框样式
   - SetShowLines(bool)：显示/隐藏连接线
   - SetShowIcons(bool)：显示/隐藏图标
   - SetShowActions(bool)：显示/隐藏操作按钮

5. 搜索功能：
   - SetAllowSearch(bool)：启用搜索功能
   - SetSearchPlaceholder(string)：设置搜索占位符
   - OnSearch(Action<string>)：搜索事件处理

6. 选择模式：
   - SetMultiSelect(bool)：启用多选模式
   - SelectItem(string)：选择项目
   - DeselectItem(string)：取消选择
   - SelectAll()：全选
   - DeselectAll()：取消全选

7. 展开控制：
   - ExpandNode(string)：展开指定节点
   - CollapseNode(string)：收起指定节点
   - ExpandAll()：展开所有节点
   - CollapseAll()：收起所有节点

8. 操作按钮：
   - AddAction(string, string, string)：添加操作按钮
   - OnAction(Action<string, string>)：操作事件处理
   - 支持图标和提示文本

9. 事件处理：
   - OnNodeClick：节点点击事件
   - OnItemSelect：项目选择事件
   - OnNodeExpand：节点展开事件
   - OnNodeCollapse：节点收起事件
   - OnAction：操作按钮点击事件

10. 最佳实践：
    - 合理组织树结构层次
    - 使用语义化的图标和文本
    - 为操作按钮提供清晰的提示
    - 考虑大数据量时的性能优化
    - 支持键盘导航和无障碍功能";
        }

        #endregion

        #region 变体支持

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "basic",
                "search",
                "multi-select",
                "compact",
                "dark",
                "file-tree",
                "project-tree",
                "settings-tree",
                "bordered",
                "no-lines"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-2");

            DaisyTree tree = null;

            switch (variant.ToLower())
            {
                case "basic":
                    tree = DaisyBuilder.Tree("basic-variant");
                    break;
                case "search":
                    tree = DaisyBuilder.SearchTree("search-variant", "搜索...");
                    break;
                case "multi-select":
                    tree = DaisyBuilder.MultiSelectTree("multi-variant");
                    break;
                case "compact":
                    tree = DaisyBuilder.CompactTree("compact-variant");
                    break;
                case "dark":
                    tree = DaisyBuilder.DarkTree("dark-variant");
                    break;
                case "file-tree":
                    tree = DaisyBuilder.FileTree("file-variant");
                    break;
                case "project-tree":
                    tree = DaisyBuilder.ProjectTree("project-variant");
                    break;
                case "settings-tree":
                    tree = DaisyBuilder.SettingsTree("settings-variant");
                    break;
                case "bordered":
                    tree = DaisyBuilder.Tree("bordered-variant");
                    tree.AddClass("bordered");
                    break;
                case "no-lines":
                    tree = DaisyBuilder.Tree("no-lines-variant");
                    tree.SetShowLines(false);
                    break;
                default:
                    return null;
            }

            if (tree != null)
            {
                // 添加示例数据
                CreateVariantSampleData(tree, variant);
                container.Add(tree);
            }

            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "basic" => @"var tree = DaisyBuilder.Tree(""basic-tree"");",
                "search" => @"var tree = DaisyBuilder.SearchTree(""search-tree"", ""搜索..."");",
                "multi-select" => @"var tree = DaisyBuilder.MultiSelectTree(""multi-tree"");",
                "compact" => @"var tree = DaisyBuilder.CompactTree(""compact-tree"");",
                "dark" => @"var tree = DaisyBuilder.DarkTree(""dark-tree"");",
                "file-tree" => @"var tree = DaisyBuilder.FileTree(""file-tree"");",
                "project-tree" => @"var tree = DaisyBuilder.ProjectTree(""project-tree"");",
                "settings-tree" => @"var tree = DaisyBuilder.SettingsTree(""settings-tree"");",
                "bordered" => @"var tree = DaisyBuilder.Tree(""bordered-tree"").SetBordered(true);",
                "no-lines" => @"var tree = DaisyBuilder.Tree(""no-lines-tree"").SetShowLines(false);",
                _ => null
            };
        }

        /// <summary>
        /// 为变体创建示例数据
        /// </summary>
        /// <param name="tree">树组件</param>
        /// <param name="variant">变体名称</param>
        private void CreateVariantSampleData(DaisyTree tree, string variant)
        {
            switch (variant.ToLower())
            {
                case "file-tree":
                    CreateSimpleFileData(tree);
                    break;
                case "project-tree":
                    CreateSimpleProjectData(tree);
                    break;
                case "settings-tree":
                    CreateSimpleSettingsData(tree);
                    break;
                default:
                    CreateSimpleTreeData(tree);
                    break;
            }
        }

        /// <summary>
        /// 创建简单树数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateSimpleTreeData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var node1 = new DaisyTreeData("n1", "节点1", "📁");
            node1.AddChild("i1", "项目1", "📄");
            node1.AddChild("i2", "项目2", "📄");

            var node2 = new DaisyTreeData("n2", "节点2", "📁");
            node2.AddChild("i3", "项目3", "📄");

            treeData.AddRange(new[] { node1, node2 });
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建简单文件数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateSimpleFileData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var folder = new DaisyTreeData("folder", "文件夹", "📁");
            folder.AddChild("file1.txt", "文件1", "📄");
            folder.AddChild("file2.png", "图片", "🖼️");

            treeData.Add(folder);
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建简单项目数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateSimpleProjectData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var scripts = new DaisyTreeData("scripts", "脚本", "📁");
            scripts.AddChild("main.cs", "主程序", "🎮");
            scripts.AddChild("ui.cs", "界面", "🎨");

            treeData.Add(scripts);
            tree.SetData(treeData);
        }

        /// <summary>
        /// 创建简单设置数据
        /// </summary>
        /// <param name="tree">树组件</param>
        private void CreateSimpleSettingsData(DaisyTree tree)
        {
            var treeData = new List<DaisyTreeData>();

            var general = new DaisyTreeData("general", "常规", "⚙️");
            general.AddChild("theme", "主题", "🎨");
            general.AddChild("language", "语言", "🌐");

            treeData.Add(general);
            tree.SetData(treeData);
        }

        #endregion
    }
}