using UnityEngine;
using UnityEngine.InputSystem;
using BlastingDesign.Core.Input;

public partial class InputManager : MonoBehaviour
{
  #region 选择输入处理
  private void HandleSelectionInput()
  {
    if (!enableSelection || selectionManager == null) return;

    // 使用事件优先级管理器检查是否应该处理选择事件
    if (useEventPriorityManager && eventPriorityManager != null)
    {
      Vector2 mousePosition = Mouse.current.position.ReadValue();
      if (!eventPriorityManager.AllowSceneInteraction(mousePosition))
      {
        // 不允许场景交互，跳过选择处理
        return;
      }
    }
    else
    {
      // 回退到原有的UI检测方法（简化版）
      bool isOverUI = IsPointerOverUI();
      if (isOverUI)
      {
        // 如果启用了调试信息，显示UI检测状态
        if (useEventSystemManager && inputSystemManager != null && inputSystemManager.showDebugInfo)
        {
          Debug.Log("鼠标在UI上，不处理选择操作");
        }
        return;
      }
    }

    // 如果正在进行视角操作，不处理选择
    if (isRotating || isPanning || isSmoothZooming) return;

    // 左键按下开始选择
    if (Mouse.current.leftButton.wasPressedThisFrame)
    {
      StartSelection();
    }

    // 左键拖拽进行框选
    if (Mouse.current.leftButton.isPressed && isSelecting)
    {
      UpdateSelection();
    }

    // 左键释放完成选择
    if (Mouse.current.leftButton.wasReleasedThisFrame && isSelecting)
    {
      EndSelection();
    }
  }
  #endregion

  #region 选择操作实现
  private void StartSelection()
  {
    Vector2 mousePosition = Mouse.current.position.ReadValue();

    // 通知EventSystemManager开始选择操作
    if (useEventSystemManager && inputSystemManager != null)
    {
      inputSystemManager.BeginSelection();
    }

    // 检查是否点击到了物体
    Ray ray = mainCamera.ScreenPointToRay(mousePosition);
    if (Physics.Raycast(ray, out RaycastHit hit, Mathf.Infinity, selectionLayerMask))
    {
      Selectable selectable = hit.collider.GetComponent<Selectable>();
      if (selectable != null)
      {
        // 单击选择物体
        bool ctrlPressed = Keyboard.current.leftCtrlKey.isPressed || Keyboard.current.rightCtrlKey.isPressed ||
                          Keyboard.current.leftCommandKey.isPressed || Keyboard.current.rightCommandKey.isPressed;

        if (!ctrlPressed)
        {
          selectionManager.ClearSelection();
        }

        selectionManager.ToggleSelection(selectable);
        isSelecting = false;

        // 通知EventSystemManager结束选择操作
        if (useEventSystemManager && inputSystemManager != null)
        {
          inputSystemManager.EndSelection();
        }
        return;
      }
    }

    // 如果没有点击到物体，开始框选
    isSelecting = true;

    // 通知事件优先级管理器开始选择操作
    if (useEventPriorityManager && eventPriorityManager != null)
    {
      eventPriorityManager.BeginDragOperation(InputEventPriorityManager.DragOperationType.Selection, mousePosition);
    }

    // 如果没有按住Ctrl/Cmd，清除之前的选择
    bool ctrlPressed2 = Keyboard.current.leftCtrlKey.isPressed || Keyboard.current.rightCtrlKey.isPressed ||
                       Keyboard.current.leftCommandKey.isPressed || Keyboard.current.rightCommandKey.isPressed;

    if (!ctrlPressed2)
    {
      selectionManager.ClearSelection();
    }

    // 通知SelectionBox开始选择
    if (SelectionBox.Instance != null)
    {
      SelectionBox.Instance.StartSelection(mousePosition);
    }
  }

  private void UpdateSelection()
  {
    Vector2 mousePosition = Mouse.current.position.ReadValue();

    // 更新SelectionBox
    if (SelectionBox.Instance != null)
    {
      SelectionBox.Instance.UpdateSelection(mousePosition);
    }
  }

  private void EndSelection()
  {
    isSelecting = false;

    // 通知事件优先级管理器结束选择操作
    if (useEventPriorityManager && eventPriorityManager != null)
    {
      eventPriorityManager.EndDragOperation();
    }

    // 结束SelectionBox
    if (SelectionBox.Instance != null)
    {
      SelectionBox.Instance.EndSelection();

      // 执行框选
      PerformBoxSelection(SelectionBox.Instance.CurrentRect);
    }

    // 通知EventSystemManager结束选择操作
    if (useEventSystemManager && inputSystemManager != null)
    {
      inputSystemManager.EndSelection();
    }
  }

  private void PerformBoxSelection(Rect selectionRect)
  {
    // 获取所有可选择的物体
    Selectable[] allSelectables = FindObjectsByType<Selectable>(FindObjectsSortMode.None);

    foreach (Selectable selectable in allSelectables)
    {
      if (IsObjectInSelectionRect(selectable, selectionRect))
      {
        selectionManager.AddToSelection(selectable);
      }
    }
  }

  private bool IsObjectInSelectionRect(Selectable selectable, Rect selectionRect)
  {
    // 使用Selectable的更精确检测方法
    return selectable.IsInScreenRect(selectionRect, mainCamera);
  }
  #endregion

  #region 选择相关公共方法
  public void EnableSelection()
  {
    enableSelection = true;
  }

  public void DisableSelection()
  {
    enableSelection = false;
    if (isSelecting)
    {
      EndSelection();
    }

    // 确保清除选择相关的拖拽状态
    if (useEventPriorityManager && eventPriorityManager != null &&
        eventPriorityManager.GetCurrentDragType() == InputEventPriorityManager.DragOperationType.Selection)
    {
      eventPriorityManager.EndDragOperation();
    }
  }

  public void ClearAllSelections()
  {
    if (selectionManager != null)
    {
      selectionManager.ClearSelection();
    }
  }
  #endregion
}
