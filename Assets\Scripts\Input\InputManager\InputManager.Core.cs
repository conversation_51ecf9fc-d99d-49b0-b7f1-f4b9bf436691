using UnityEngine;
using UnityEngine.InputSystem;
using UnityEngine.EventSystems;
using BlastingDesign.Core.Input;
using BlastingDesign.Core.Camera;
using DG.Tweening;

/// <summary>
/// InputManager核心类 - 基本生命周期和运行时状态管理
/// 
/// 此类已拆分为多个partial文件：
/// - InputManager.Core.cs: 核心运行时字段和生命周期
/// - InputManager.Configuration.cs: 配置字段和验证
/// - InputManager.Initialization.cs: 初始化方法
/// - InputManager.CesiumControl.cs: Cesium相关功能
/// - InputManager.KeyboardMovement.cs: 键盘移动控制
/// - InputManager.Utilities.cs: 工具方法和公共接口
/// </summary>
public partial class InputManager : MonoBehaviour
{
    #region 核心运行时字段

    // InputSystem相关
    private InputActionMap playerActionMap;
    private InputActionMap uiActionMap;
    private InputAction moveAction;
    private InputAction lookAction;
    private InputAction scrollAction;
    private InputAction verticalMoveAction;

    // 状态标志
    private bool isUIActive = false;
    private bool isRotating = false;
    private bool isPanning = false;
    private bool isSmoothZooming = false;
    private bool isSelecting = false;
    private bool isKeyboardMoving = false;
    private bool justWrapped = false;

    // 运行时位置和距离
    private Vector2 lastMousePosition;
    private Vector3 rotationCenterPosition;
    private float currentDistance;
    private Vector3 velocity = Vector3.zero;

    // 鼠标包裹相关
    private float lastWrapTime = 0f;

    // 缩放缓动相关变量
    private bool isZoomEasing = false;
    private float zoomEaseStartTime;
    private Vector3 zoomStartPosition;
    private Vector3 zoomTargetPosition;
    private Tween currentZoomTween;

    // 系统引用（运行时）
    private InputSystemManager inputSystemManager;
    private InputEventPriorityManager eventPriorityManager;
    private CameraHeightAdaptationSystem cameraHeightAdaptationSystem;

    #endregion

    #region 生命周期方法

    void Awake()
    {
        InitializeInputSystem();
        InitializeCamera();
        InitializeManagers();
    }

    void OnEnable()
    {
        EnablePlayerInput();
    }

    void OnDisable()
    {
        DisableAllInput();
        CleanupDOTween();
    }

    void Start()
    {
        ValidateMovementSettings();
    }

    void Update()
    {
        HandleInputModeToggle();
        HandleTerrainToggle();
        UpdateCesiumInteractionSphere();

        if (!ShouldPauseKeyboardMovement())
        {
            HandleKeyboardMovement();
            HandleViewportControls();
            HandleSelectionInput();
        }
    }

    #endregion

    #region 核心输入处理方法

    /// <summary>
    /// 处理输入模式切换
    /// </summary>
    private void HandleInputModeToggle()
    {
        // 只有在TextField没有焦点时才处理Tab键
        if (!IsAnyTextFieldFocused() && Keyboard.current.tabKey.wasPressedThisFrame)
        {
            ToggleInputMode();
        }
    }

    /// <summary>
    /// 处理地形切换
    /// </summary>
    private void HandleTerrainToggle()
    {
        // 只有在TextField没有焦点时才处理地形切换键
        if (!IsAnyTextFieldFocused() && Input.GetKeyDown(terrainToggleKey))
        {
            ToggleCesiumTerrain();
        }
    }

    #endregion

    #region 公共状态查询方法

    public bool IsViewportControlActive()
    {
        return isRotating || isPanning || isSmoothZooming;
    }

    public bool IsSelectionActive()
    {
        return isSelecting;
    }

    public SelectionManager GetSelectionManager()
    {
        return selectionManager;
    }

    public bool IsMouseWrappingEnabled()
    {
        return enableMouseWrapping;
    }

    public CameraHeightAdaptationSystem GetCameraHeightAdaptationSystem()
    {
        return cameraHeightAdaptationSystem;
    }

    public void SetCameraHeightAdaptationEnabled(bool enabled)
    {
        if (cameraHeightAdaptationSystem != null)
        {
            cameraHeightAdaptationSystem.SetEnabled(enabled);
        }
    }

    public void DiagnoseCameraHeightAdaptation()
    {
        if (cameraHeightAdaptationSystem != null)
        {
            cameraHeightAdaptationSystem.DiagnoseSystem();
        }
        else
        {
            Debug.LogWarning("CameraHeightAdaptationSystem 未初始化！");
        }
    }

    #endregion

    #region DOTween清理

    /// <summary>
    /// 清理DOTween动画
    /// </summary>
    private void CleanupDOTween()
    {
        if (currentZoomTween != null && currentZoomTween.IsActive())
        {
            currentZoomTween.Kill();
            currentZoomTween = null;
        }
        isZoomEasing = false;
    }

    #endregion
}