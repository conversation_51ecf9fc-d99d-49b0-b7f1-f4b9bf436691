using UnityEngine;
using UnityEngine.UIElements;
using BlastingDesign.UI.Core;
using BlastingDesign.UI.Components;
using BlastingDesign.Utils;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;

public class UIManager : MonoBehaviour
{
  [Header("Main Layout")]
  public VisualTreeAsset mainLayoutAsset;
  public StyleSheet mainStyleSheet;

  [Header("Test Mode")]
  public bool useTestLayout = false;
  public VisualTreeAsset testLayoutAsset;

  [Header("Debug Settings")]
  public bool debugMode = false;

  [Header("Event Blocking Settings")]
  public bool enableUIEventBlocking = true; // 是否启用UI事件阻断
  public bool debugEventBlocking = false; // 是否启用事件阻断调试日志

  [Header("Camera Settings")]
  public Camera sceneCamera;
  public RenderTexture cameraRenderTexture;

  // UI系统
  private VisualElement root;
  private Camera mainCamera;

  // UI组件
  private TopToolbar topToolbar;
  private LeftPanel leftPanel;
  private RightPanel rightPanel;
  private StatusBar statusBar;

  // 容器引用
  private VisualElement topToolbarContainer;
  private VisualElement leftPanelContainer;
  private VisualElement rightPanelContainer;
  private VisualElement statusBarContainer;

  void Start()
  {
    Logging.LogInfo("UIManager", "开始初始化...");

    // 初始化事件系统
    InitializeEventSystem();

    // 设置主布局
    SetupMainLayout();

    // 如果主布局设置成功，再初始化组件
    if (root != null)
    {
      // 初始化UI组件
      InitializeComponents();
    }
    else
    {
      Logging.LogError("UIManager", "主布局设置失败，跳过组件初始化");
    }

    // 设置相机
    SetupCamera();

    // 设置UI事件阻断
    SetupUIEventBlocking();

    Logging.LogInfo("UIManager", "模块化UI系统初始化完成！");
  }



  private void InitializeEventSystem()
  {
    // 确保新事件系统已初始化
    var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
    if (eventSystemManager == null)
    {
      Logging.LogWarning("UIManager", "EventSystemManager未找到，请确保场景中有EventSystemManager组件");
    }
    else if (eventSystemManager.EventBus == null)
    {
      Logging.LogWarning("UIManager", "EventBus未初始化");
    }
    else
    {
      Logging.LogInfo("UIManager", "新事件系统已就绪");
    }
  }

  private void SetupMainLayout()
  {
    Logging.LogInfo("UIManager", "开始设置主布局...");

    // 获取UI Document组件
    var uiDocument = GetComponent<UIDocument>();
    if (uiDocument == null)
    {
      Logging.LogInfo("UIManager", "添加UIDocument组件");
      uiDocument = gameObject.AddComponent<UIDocument>();
    }

    // 选择要使用的布局资源
    VisualTreeAsset layoutAsset = useTestLayout ? testLayoutAsset : mainLayoutAsset;

    if (layoutAsset == null)
    {
      if (useTestLayout)
      {
        Logging.LogError("UIManager", "测试模式已启用，但未设置Test Layout Asset！");
      }
      else
      {
        Logging.LogError("UIManager", "请在Inspector中设置Main Layout Asset！");
      }
      return;
    }

    Logging.LogInfo("UIManager", $"设置UXML资源: {layoutAsset.name} (测试模式: {useTestLayout})");
    // 设置Visual Tree Asset
    uiDocument.visualTreeAsset = layoutAsset;

    // 获取根元素
    root = uiDocument.rootVisualElement;
    if (root == null)
    {
      Logging.LogError("UIManager", "无法获取根UI元素！");
      return;
    }

    Logging.LogInfo("UIManager", $"根元素获取成功，子元素数量: {root.childCount}");

    // 应用主样式表
    if (mainStyleSheet != null)
    {
      root.styleSheets.Add(mainStyleSheet);
      Logging.LogInfo("UIManager", $"应用主样式表: {mainStyleSheet.name}");
    }
    else
    {
      Logging.LogWarning("UIManager", "主样式表未设置");
    }

    // 获取容器引用
    topToolbarContainer = root.Q<VisualElement>("top-toolbar-container");
    leftPanelContainer = root.Q<VisualElement>("left-panel-container");
    rightPanelContainer = root.Q<VisualElement>("right-panel-container");
    statusBarContainer = root.Q<VisualElement>("status-bar-container");

    // 验证容器是否找到
    Logging.LogInfo("UIManager", "容器查找结果:");
    Logging.LogInfo("UIManager", $"  - TopToolbar容器: {(topToolbarContainer != null ? "找到" : "未找到")}");
    Logging.LogInfo("UIManager", $"  - LeftPanel容器: {(leftPanelContainer != null ? "找到" : "未找到")}");
    Logging.LogInfo("UIManager", $"  - RightPanel容器: {(rightPanelContainer != null ? "找到" : "未找到")}");
    Logging.LogInfo("UIManager", $"  - StatusBar容器: {(statusBarContainer != null ? "找到" : "未找到")}");

    Logging.LogInfo("UIManager", "主布局设置完成！");
  }

  private void InitializeComponents()
  {
    if (useTestLayout)
    {
      Logging.LogInfo("UIManager", "测试模式：跳过组件初始化");
      return;
    }

    Logging.LogInfo("UIManager", "开始初始化轻量级UI组件...");

    try
    {
      // 创建轻量级组件实例
      CreateLightweightComponents();

      // 添加组件到布局
      AddComponentsToLayout();

      Logging.LogInfo("UIManager", "所有轻量级UI组件初始化完成");
    }
    catch (System.Exception ex)
    {
      Logging.LogError("UIManager", $"组件初始化失败: {ex.Message}");
    }
  }

  private void CreateLightweightComponents()
  {
    Logging.LogInfo("UIManager", "开始创建轻量级组件实例");

    // 创建TopToolbar（轻量级）
    topToolbar = new TopToolbar();
    Logging.LogInfo("UIManager", "TopToolbar组件已创建");

    // 创建LeftPanel（轻量级）
    leftPanel = new LeftPanel();
    Logging.LogInfo("UIManager", "LeftPanel组件已创建");

    // 创建RightPanel（轻量级）
    rightPanel = new RightPanel();
    Logging.LogInfo("UIManager", "RightPanel组件已创建");

    // 创建StatusBar（轻量级）
    statusBar = new StatusBar();
    Logging.LogInfo("UIManager", "StatusBar组件已创建");

    Logging.LogInfo("UIManager", "所有轻量级组件实例创建完成，等待初始化");
  }

  private void AddComponentsToLayout()
  {
    Logging.LogInfo("UIManager", "开始添加组件到布局");

    // 添加TopToolbar
    if (topToolbarContainer != null && topToolbar != null)
    {
      topToolbarContainer.Add(topToolbar);
      Logging.LogInfo("UIManager", "TopToolbar已添加到容器");

      // 添加到UI树后，延迟初始化
      topToolbar.InitializeDelayed();
      Logging.LogInfo("UIManager", "TopToolbar延迟初始化已安排");
    }
    else
    {
      Logging.LogWarning("UIManager", "TopToolbar容器未找到或组件为空");
    }

    // 添加LeftPanel
    if (leftPanelContainer != null && leftPanel != null)
    {
      leftPanelContainer.Add(leftPanel);
      Logging.LogInfo("UIManager", "LeftPanel已添加到容器");

      // 添加到UI树后，延迟初始化
      leftPanel.InitializeDelayed();
      Logging.LogInfo("UIManager", "LeftPanel延迟初始化已安排");
    }
    else
    {
      Logging.LogWarning("UIManager", "LeftPanel容器未找到或组件为空");
    }

    // 添加RightPanel（轻量级组件）
    if (rightPanelContainer != null && rightPanel != null)
    {
      rightPanelContainer.Add(rightPanel);
      Logging.LogInfo("UIManager", "RightPanel已添加到容器");

      // 添加到UI树后，延迟初始化
      rightPanel.InitializeDelayed();
      Logging.LogInfo("UIManager", "RightPanel延迟初始化已安排");
    }
    else
    {
      Logging.LogWarning("UIManager", "RightPanel容器未找到或组件为空");
    }

    // 添加StatusBar（轻量级组件）
    if (statusBarContainer != null && statusBar != null)
    {
      statusBarContainer.Add(statusBar);
      Logging.LogInfo("UIManager", "StatusBar已添加到容器");

      // 添加到UI树后，延迟初始化
      statusBar.InitializeDelayed();
      Logging.LogInfo("UIManager", "StatusBar延迟初始化已安排");
    }
    else
    {
      Logging.LogWarning("UIManager", "StatusBar容器未找到或组件为空");
    }
  }

  private void SetupCamera()
  {
    // 确保主相机正常渲染到屏幕
    mainCamera = Camera.main;
    if (mainCamera == null)
    {
      mainCamera = FindFirstObjectByType<Camera>();
    }

    if (mainCamera != null)
    {
      // 确保主相机渲染到屏幕而不是RenderTexture
      mainCamera.targetTexture = null;
      Logging.LogInfo("UIManager", $"设置主相机 {mainCamera.name} 直接渲染到屏幕");
    }

    Logging.LogInfo("UIManager", "相机设置完成 - 中间区域透明，显示底层相机");
  }

  private void SetupUIEventBlocking()
  {
    if (!enableUIEventBlocking || root == null)
    {
      Logging.LogInfo("UIManager", "UI事件阻断已禁用或根元素为空");
      return;
    }

    Logging.LogInfo("UIManager", "开始设置UI事件阻断...");

    try
    {
      // 不对ScrollView添加事件阻断，让它们保持正常滚动功能
      // UIEventBlocker.AddEventBlockerForType<ScrollView>(root, debugEventBlocking);

      // 为所有DropdownField添加事件阻断
      UIEventBlocker.AddEventBlockerForType<DropdownField>(root, debugEventBlocking);

      // 为所有Slider添加事件阻断
      UIEventBlocker.AddEventBlockerForType<Slider>(root, debugEventBlocking);

      // 为所有TextField添加事件阻断
      UIEventBlocker.AddEventBlockerForType<TextField>(root, debugEventBlocking);

      // 不对左右面板容器添加滚轮事件阻断，让ScrollView能正常工作
      // 场景交互的阻断由InputEventPriorityManager处理
      // if (leftPanelContainer != null)
      // {
      //   UIEventBlocker.AddWheelEventBlocker(leftPanelContainer, debugEventBlocking, true);
      // }

      // if (rightPanelContainer != null)
      // {
      //   UIEventBlocker.AddWheelEventBlocker(rightPanelContainer, debugEventBlocking, true);
      // }

      // 为顶部工具栏和状态栏添加滚轮事件阻断
      if (topToolbarContainer != null)
      {
        UIEventBlocker.AddWheelEventBlocker(topToolbarContainer, debugEventBlocking, true);
      }

      if (statusBarContainer != null)
      {
        UIEventBlocker.AddWheelEventBlocker(statusBarContainer, debugEventBlocking, true);
      }

      Logging.LogInfo("UIManager", "UI事件阻断设置完成");
    }
    catch (System.Exception ex)
    {
      Logging.LogError("UIManager", $"UI事件阻断设置失败: {ex.Message}");
    }
  }

  #region 事件发布辅助方法

  /// <summary>
  /// 发布自定义事件
  /// </summary>
  private void PublishCustomEvent(string eventName, object data = null)
  {
    var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
    if (eventSystemManager?.EventBus != null)
    {
      eventSystemManager.EventBus.Publish(new CustomEvent(eventName, data, "UIManager"));
    }
    else
    {
      Logging.LogWarning("UIManager", "EventSystemManager未初始化，无法发布自定义事件");
    }
  }

  /// <summary>
  /// 发布状态消息事件
  /// </summary>
  private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
  {
    var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
    if (eventSystemManager?.EventBus != null)
    {
      eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "UIManager"));
    }
    else
    {
      Logging.LogWarning("UIManager", "EventSystemManager未初始化，无法发布状态消息事件");
    }
  }

  /// <summary>
  /// 发布鼠标世界位置事件
  /// </summary>
  private void PublishMouseWorldPosition(Vector3 worldPosition)
  {
    var eventSystemManager = BlastingDesign.Events.EventSystemManager.Instance;
    if (eventSystemManager?.EventBus != null)
    {
      eventSystemManager.EventBus.Publish(new CustomEvent("MouseWorldPosition", worldPosition, "UIManager"));
    }
    else
    {
      Logging.LogWarning("UIManager", "EventSystemManager未初始化，无法发布鼠标位置事件");
    }
  }

  #endregion

  #region 公共API方法

  /// <summary>
  /// 显示通知消息
  /// </summary>
  public void ShowNotification(string message, float duration = 3f)
  {
    // 通过新事件系统发送通知
    PublishCustomEvent("ShowNotification", new { message, duration });
    Logging.LogInfo("UIManager", $"显示通知: {message}");
  }

  /// <summary>
  /// 显示进度
  /// </summary>
  public void ShowProgress(string message, float progress = 0f)
  {
    // 通过新事件系统发送进度更新
    PublishCustomEvent("ShowProgress", new { progress, message });
    Logging.LogInfo("UIManager", $"显示进度: {progress:P0} - {message}");
  }

  /// <summary>
  /// 更新进度
  /// </summary>
  public void UpdateProgress(float progress)
  {
    PublishCustomEvent("UpdateProgress", progress);
    Logging.LogInfo("UIManager", $"更新进度: {progress:P0}");
  }

  /// <summary>
  /// 隐藏进度
  /// </summary>
  public void HideProgress()
  {
    PublishCustomEvent("HideProgress", null);
    Logging.LogInfo("UIManager", "隐藏进度");
  }

  /// <summary>
  /// 更新缩放级别
  /// </summary>
  public void UpdateZoomLevel(float zoomLevel)
  {
    PublishCustomEvent("ZoomLevelChanged", zoomLevel);
    Logging.LogInfo("UIManager", $"更新缩放级别: {zoomLevel:F1}x");
  }

  /// <summary>
  /// 更新鼠标世界位置
  /// </summary>
  public void UpdateMouseWorldPosition(Vector3 worldPosition)
  {
    PublishMouseWorldPosition(worldPosition);
  }

  /// <summary>
  /// 更新状态消息
  /// </summary>
  public void UpdateStatus(string status)
  {
    PublishStatusMessage(status);
    Logging.LogInfo("UIManager", $"更新状态: {status}");
  }

  #endregion

  void OnDestroy()
  {
    Logging.LogInfo("UIManager", "开始清理UI组件");

    // 清理轻量级组件
    topToolbar?.Cleanup();
    leftPanel?.Cleanup();
    rightPanel?.Cleanup();
    statusBar?.Cleanup();

    // 清理RenderTexture
    if (cameraRenderTexture != null)
    {
      cameraRenderTexture.Release();
    }

    Logging.LogInfo("UIManager", "UI组件清理完成");
  }
}