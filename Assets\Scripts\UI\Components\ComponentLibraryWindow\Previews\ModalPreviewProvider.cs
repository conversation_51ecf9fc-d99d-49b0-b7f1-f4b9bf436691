using UnityEngine.UIElements;
using BlastingDesign.UI.DaisyUI.Builders;
using BlastingDesign.UI.Extensions;
using BlastingDesign.UI.Core;
using BlastingDesign.Events;
using BlastingDesign.Events.Core;
using BlastingDesign.Utils;

namespace BlastingDesign.UI.Components.Previews
{
    /// <summary>
    /// 模态框组件预览提供者
    /// </summary>
    public class ModalPreviewProvider : BaseComponentPreviewProvider
    {
        #region 基础信息

        public override string ComponentId => "modal-item";
        public override string ComponentName => "模态框 (Modal)";
        public override string ComponentDescription => "模态框组件用于显示叠加内容，支持多种尺寸和样式，可用于对话框、确认框等场景";

        #endregion

        #region 事件发布辅助方法

        /// <summary>
        /// 发布状态消息事件
        /// </summary>
        private void PublishStatusMessage(string message, StatusMessageType messageType = StatusMessageType.Info)
        {
            var eventSystemManager = EventSystemManager.Instance;
            if (eventSystemManager?.EventBus != null)
            {
                eventSystemManager.EventBus.Publish(new StatusMessageEvent(message, messageType, 3f, "ModalPreviewProvider"));
            }
            else
            {
                Logging.LogWarning("ModalPreviewProvider", "EventSystemManager未初始化，无法发布状态消息事件");
            }
        }

        #endregion

        public override VisualElement CreatePreview()
        {
            var container = CreatePreviewContainer("flex", "flex-col", "gap-4");

            // 基本模态框组
            var basicSection = new VisualElement();
            basicSection.AddToClassList("preview-section");

            var basicTitle = CreatePreviewTitle("基本模态框");
            basicSection.Add(basicTitle);

            var basicModalContainer = CreatePreviewContainer("flex", "flex-col", "gap-3");

            // 基本模态框触发按钮
            var basicModalBtn = DaisyBuilder.Button("打开基本模态框")
                .SetPrimary()
                .OnClick(() =>
                {
                    PublishStatusMessage("基本模态框演示 - 在实际应用中会打开模态框");
                });
            basicModalContainer.Add(basicModalBtn);

            var basicDescription = new Label("点击按钮可以打开一个基本的模态框窗口")
                .AddDaisyClass("text-sm")
                .AddDaisyClass("text-gray-500");
            basicModalContainer.Add(basicDescription);

            basicSection.Add(basicModalContainer);
            container.Add(basicSection);

            // 模态框尺寸组
            var sizeSection = new VisualElement();
            sizeSection.AddToClassList("preview-section");

            var sizeTitle = CreatePreviewTitle("模态框尺寸");
            sizeSection.Add(sizeTitle);

            var sizeModalContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2");

            // 小模态框
            var smallModalBtn = DaisyBuilder.Button("小模态框")
                .SetSecondary()
                .SetSmall()
                .OnClick(() => PublishStatusMessage("小尺寸模态框演示"));
            sizeModalContainer.Add(smallModalBtn);

            // 中等模态框
            var mediumModalBtn = DaisyBuilder.Button("中等模态框")
                .SetSecondary()
                .OnClick(() => PublishStatusMessage("中等尺寸模态框演示"));
            sizeModalContainer.Add(mediumModalBtn);

            // 大模态框
            var largeModalBtn = DaisyBuilder.Button("大模态框")
                .SetSecondary()
                .OnClick(() => PublishStatusMessage("大尺寸模态框演示"));
            sizeModalContainer.Add(largeModalBtn);

            // 全屏模态框
            var fullscreenModalBtn = DaisyBuilder.Button("全屏模态框")
                .SetSecondary()
                .OnClick(() => PublishStatusMessage("全屏模态框演示"));
            sizeModalContainer.Add(fullscreenModalBtn);

            sizeSection.Add(sizeModalContainer);
            container.Add(sizeSection);

            // 模态框类型组
            var typeSection = new VisualElement();
            typeSection.AddToClassList("preview-section");

            var typeTitle = CreatePreviewTitle("模态框类型");
            typeSection.Add(typeTitle);

            var typeModalContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2");

            // 信息模态框
            var infoModalBtn = DaisyBuilder.Button("信息模态框")
                .SetInfo()
                .OnClick(() => PublishStatusMessage("信息模态框演示"));
            typeModalContainer.Add(infoModalBtn);

            // 成功模态框
            var successModalBtn = DaisyBuilder.Button("成功模态框")
                .SetSuccess()
                .OnClick(() => PublishStatusMessage("成功模态框演示"));
            typeModalContainer.Add(successModalBtn);

            // 警告模态框
            var warningModalBtn = DaisyBuilder.Button("警告模态框")
                .SetWarning()
                .OnClick(() => PublishStatusMessage("警告模态框演示", StatusMessageType.Warning));
            typeModalContainer.Add(warningModalBtn);

            // 错误模态框
            var errorModalBtn = DaisyBuilder.Button("错误模态框")
                .SetError()
                .OnClick(() => PublishStatusMessage("错误模态框演示", StatusMessageType.Error));
            typeModalContainer.Add(errorModalBtn);

            typeSection.Add(typeModalContainer);
            container.Add(typeSection);

            // 确认对话框组
            var confirmSection = new VisualElement();
            confirmSection.AddToClassList("preview-section");

            var confirmTitle = CreatePreviewTitle("确认对话框");
            confirmSection.Add(confirmTitle);

            var confirmModalContainer = CreatePreviewContainer("flex", "flex-wrap", "gap-2");

            // 确认删除
            var deleteConfirmBtn = DaisyBuilder.Button("删除确认")
                .SetError()
                .OnClick(() => PublishStatusMessage("删除确认对话框演示", StatusMessageType.Warning));
            confirmModalContainer.Add(deleteConfirmBtn);

            // 保存确认
            var saveConfirmBtn = DaisyBuilder.Button("保存确认")
                .SetSuccess()
                .OnClick(() => PublishStatusMessage("保存确认对话框演示"));
            confirmModalContainer.Add(saveConfirmBtn);

            confirmSection.Add(confirmModalContainer);
            container.Add(confirmSection);

            return container;
        }

        public override string GetCodeExample()
        {
            return @"// 基本用法
using BlastingDesign.UI.Modal;
using BlastingDesign.UI.DaisyUI.Builders;

// 创建模态框管理器实例
var modalManager = ModalWindowManager.Instance;

// 创建基本模态框
var modal = modalManager.CreateWindow(""模态框标题"", new Vector2(400, 300));

// 设置模态框内容
var content = new VisualElement();
content.Add(new Label(""这是模态框的内容""));

var closeButton = DaisyBuilder.Button(""关闭"")
    .SetPrimary()
    .OnClick(() => modal.Close());
content.Add(closeButton);

modal.SetContent(content);

// 显示模态框
modal.Show();

// 创建确认对话框
var confirmModal = modalManager.CreateConfirmDialog(
    ""确认操作"",
    ""您确定要执行此操作吗？"",
    ""确认"",
    ""取消"",
    () => Debug.Log(""用户确认""),
    () => Debug.Log(""用户取消"")
);

// 创建信息对话框
var infoModal = modalManager.CreateInfoDialog(
    ""信息"",
    ""操作已成功完成！"",
    ""确定"",
    () => Debug.Log(""用户确认"")
);

// 设置模态框属性
modal.SetResizable(true);       // 可调整大小
modal.SetDraggable(true);       // 可拖拽
modal.SetModal(true);           // 模态显示
modal.SetCloseOnEscape(true);   // ESC键关闭

// 事件处理
modal.OnWindowClosed += () => Debug.Log(""模态框已关闭"");
modal.OnWindowShown += () => Debug.Log(""模态框已显示"");";
        }

        public override string GetUsageInstructions()
        {
            return @"模态框组件使用说明：

1. 基本用法：
   - 使用 ModalWindowManager.Instance 获取管理器
   - 调用 CreateWindow() 创建模态框
   - 设置内容并显示

2. 模态框类型：
   - CreateWindow()：基本模态框
   - CreateConfirmDialog()：确认对话框
   - CreateInfoDialog()：信息对话框
   - CreateErrorDialog()：错误对话框

3. 尺寸选项：
   - 小尺寸：300x200
   - 中等尺寸：500x400（默认）
   - 大尺寸：800x600
   - 全屏：屏幕尺寸

4. 属性设置：
   - SetResizable(bool)：设置可调整大小
   - SetDraggable(bool)：设置可拖拽
   - SetModal(bool)：设置模态显示
   - SetCloseOnEscape(bool)：ESC键关闭

5. 内容管理：
   - SetContent(VisualElement)：设置内容
   - AddContent(VisualElement)：添加内容
   - ClearContent()：清空内容

6. 生命周期：
   - Show()：显示模态框
   - Hide()：隐藏模态框
   - Close()：关闭模态框
   - Destroy()：销毁模态框

7. 事件处理：
   - OnWindowShown：显示事件
   - OnWindowHidden：隐藏事件
   - OnWindowClosed：关闭事件

8. 最佳实践：
   - 合理使用模态框避免过度打扰用户
   - 提供明确的关闭方式
   - 考虑模态框的层级关系
   - 注意内存管理和事件清理";
        }

        public override string[] GetSupportedVariants()
        {
            return new string[]
            {
                "basic",
                "confirm",
                "info",
                "warning",
                "error",
                "small",
                "large",
                "fullscreen"
            };
        }

        public override VisualElement CreateVariantPreview(string variant)
        {
            var container = CreatePreviewContainer("flex", "gap-2");

            var button = DaisyBuilder.Button($"{variant.ToUpper()} Modal");

            switch (variant.ToLower())
            {
                case "basic":
                    button.SetPrimary();
                    break;
                case "confirm":
                    button.SetWarning();
                    break;
                case "info":
                    button.SetInfo();
                    break;
                case "warning":
                    button.SetWarning();
                    break;
                case "error":
                    button.SetError();
                    break;
                case "small":
                    button.SetSecondary().SetSmall();
                    break;
                case "large":
                    button.SetSecondary();
                    break;
                case "fullscreen":
                    button.SetSecondary();
                    break;
                default:
                    return null;
            }

            button.OnClick(() => PublishStatusMessage($"{variant} 模态框演示"));
            container.Add(button);
            return container;
        }

        public override string GetVariantCodeExample(string variant)
        {
            return variant.ToLower() switch
            {
                "basic" => @"var modal = ModalWindowManager.Instance.CreateWindow(""基本模态框"", new Vector2(400, 300));",
                "confirm" => @"var modal = ModalWindowManager.Instance.CreateConfirmDialog(""确认"", ""确认操作？"", ""是"", ""否"", onConfirm, onCancel);",
                "info" => @"var modal = ModalWindowManager.Instance.CreateInfoDialog(""信息"", ""操作完成"", ""确定"", onOk);",
                "warning" => @"var modal = ModalWindowManager.Instance.CreateWarningDialog(""警告"", ""请注意"", ""确定"", onOk);",
                "error" => @"var modal = ModalWindowManager.Instance.CreateErrorDialog(""错误"", ""操作失败"", ""确定"", onOk);",
                "small" => @"var modal = ModalWindowManager.Instance.CreateWindow(""小模态框"", new Vector2(300, 200));",
                "large" => @"var modal = ModalWindowManager.Instance.CreateWindow(""大模态框"", new Vector2(800, 600));",
                "fullscreen" => @"var modal = ModalWindowManager.Instance.CreateFullscreenWindow(""全屏模态框"");",
                _ => null
            };
        }
    }
}
